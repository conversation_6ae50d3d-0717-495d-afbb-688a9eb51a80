import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";

export async function GET() {
  try {
    const raporty = await prisma.raportProdukcyjny.findMany({
      include: {
        produkt: true,
        maszyna: true,
        pracownik: true,
        surowiec: true,
        material: true,
      },
      orderBy: {
        data_utworzenia: "desc",
      },
    });

    return NextResponse.json(raporty);
  } catch (error) {
    console.error("Błąd podczas pobierania raportów:", error);
    return NextResponse.json(
      { error: "Błąd podczas pobierania raportów" },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const data = await request.json();

    const raport = await prisma.raportProdukcyjny.create({
      data: {
        produkt_id: data.produkt_id,
        metraz_rolek: data.metraz_rolek,
        ilosc_rolek: data.ilosc_rolek,
        surowiec_id: data.surowiec_id,
        zuzyty_surowiec: data.zuzyty_surowiec,
        odpad_surowiec: data.odpad_surowiec,
        material_id: data.material_id,
        zuzyty_material: data.zuzyty_material,
        odpad_material: data.odpad_material,
        maszyna_id: data.maszyna_id,
        pracownik_id: data.pracownik_id,
        czas_pracy_maszyny: data.czas_pracy_maszyny,
        czas_pracy_pracownika: data.czas_pracy_pracownika,
        uwagi: data.uwagi || null,
      },
      include: {
        produkt: true,
        maszyna: true,
        pracownik: true,
        surowiec: true,
        material: true,
      },
    });

    return NextResponse.json(raport, { status: 201 });
  } catch (error) {
    console.error("Błąd podczas tworzenia raportu:", error);
    return NextResponse.json(
      { error: "Błąd podczas tworzenia raportu" },
      { status: 500 }
    );
  }
}
