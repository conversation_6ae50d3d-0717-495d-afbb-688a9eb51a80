import { NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";

export async function GET() {
  try {
    const materialy = await prisma.material.findMany({
      orderBy: {
        nazwa: "asc",
      },
    });

    return NextResponse.json(materialy);
  } catch (error) {
    console.error("Błąd podczas pobierania materiałów:", error);
    return NextResponse.json(
      { error: "Błąd podczas pobierania materiałów" },
      { status: 500 }
    );
  }
}
