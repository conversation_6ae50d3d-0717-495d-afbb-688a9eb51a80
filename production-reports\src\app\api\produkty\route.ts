import { NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";

export async function GET() {
  try {
    console.log("Fetching produkty from database...");

    // Try direct database access first
    let produkty;
    try {
      // Use better-sqlite3 directly to bypass any Prisma issues
      const Database = require("better-sqlite3");
      const path = require("path");

      const dbPath = path.join(process.cwd(), "prisma", "baza_danych.db");
      const db = new Database(dbPath);

      console.log("Using direct database access...");
      produkty = db
        .prepare("SELECT * FROM produkty ORDER BY kod_handlowy ASC")
        .all();

      console.log(`Direct DB query successful from ${usedPath}`);
      console.log("Raw DB result:", JSON.stringify(produkty, null, 2));

      db.close();
    } catch (directDbError) {
      console.log("Direct DB failed, trying Prisma:", directDbError);

      // Fallback to Prisma
      try {
        produkty = await prisma.produkty.findMany({
          orderBy: {
            kod_handlowy: "asc",
          },
        });
      } catch (prismaError) {
        console.log("Prisma query failed, trying raw query:", prismaError);
        // Fallback to raw query
        produkty =
          await prisma.$queryRaw`SELECT * FROM produkty ORDER BY kod_handlowy ASC`;
      }
    }

    console.log("Produkty fetched:", produkty);
    console.log("Number of products:", produkty.length);
    console.log("First product:", JSON.stringify(produkty[0], null, 2));
    console.log("First product wydajnosc:", produkty[0]?.wydajnosc);
    console.log("First product ID:", produkty[0]?.id);

    // Check each product
    produkty.forEach((product, index) => {
      console.log(
        `Product ${index + 1}: ID=${product.id}, Code=${
          product.kod_handlowy
        }, Wydajnosc=${product.wydajnosc}`
      );
    });

    return NextResponse.json(produkty);
  } catch (error) {
    console.error("Błąd podczas pobierania produktów:", error);
    return NextResponse.json(
      { error: "Błąd podczas pobierania produktów" },
      { status: 500 }
    );
  }
}
