import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id);
    const data = await request.json();

    const material = await prisma.material.update({
      where: { id },
      data: {
        nazwa: data.nazwa,
        typ: data.typ || null,
        info: data.info || null,
      },
    });

    return NextResponse.json(material);
  } catch (error) {
    console.error("Błąd podczas aktualizacji materiału:", error);
    return NextResponse.json(
      { error: "Błąd podczas aktualizacji materiału" },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id);

    await prisma.material.delete({
      where: { id },
    });

    return NextResponse.json({ message: "<PERSON><PERSON><PERSON> został usunięty" });
  } catch (error) {
    console.error("Błąd podczas usuwania materiału:", error);
    return NextResponse.json(
      { error: "Błąd podczas usuwania materiału" },
      { status: 500 }
    );
  }
}
