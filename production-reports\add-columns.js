const Database = require("better-sqlite3");
const path = require("path");

console.log("Starting database update...");

try {
  const dbPath = path.join(__dirname, "baza_danych.db");
  console.log("Database path:", dbPath);
  
  const db = new Database(dbPath);
  console.log("Connected to database");

  // Check current table structure
  console.log("Checking current table structure...");
  const tableInfo = db.prepare("PRAGMA table_info(raporty_produkcyjne)").all();
  console.log("Current columns:", tableInfo.map(col => `${col.name} (${col.type})`));

  const existingColumns = tableInfo.map(col => col.name);

  // Add missing columns one by one
  if (!existingColumns.includes('nr_zamowienia')) {
    console.log("Adding nr_zamowienia column...");
    db.exec("ALTER TABLE raporty_produkcyjne ADD COLUMN nr_zamowienia TEXT;");
    console.log("✓ Added nr_zamowienia column");
  } else {
    console.log("✓ nr_zamowienia column already exists");
  }

  if (!existingColumns.includes('waga')) {
    console.log("Adding waga column...");
    db.exec("ALTER TABLE raporty_produkcyjne ADD COLUMN waga REAL;");
    console.log("✓ Added waga column");
  } else {
    console.log("✓ waga column already exists");
  }

  if (!existingColumns.includes('szarza_surowca')) {
    console.log("Adding szarza_surowca column...");
    db.exec("ALTER TABLE raporty_produkcyjne ADD COLUMN szarza_surowca TEXT;");
    console.log("✓ Added szarza_surowca column");
  } else {
    console.log("✓ szarza_surowca column already exists");
  }

  // Verify the changes
  console.log("Verifying changes...");
  const updatedTableInfo = db.prepare("PRAGMA table_info(raporty_produkcyjne)").all();
  console.log("Updated columns:", updatedTableInfo.map(col => `${col.name} (${col.type})`));

  // Test a simple query
  console.log("Testing database access...");
  const count = db.prepare("SELECT COUNT(*) as count FROM raporty_produkcyjne").get();
  console.log(`Found ${count.count} existing reports`);

  db.close();
  console.log("✅ Database update completed successfully!");

} catch (error) {
  console.error("❌ Error updating database:", error);
  console.error("Error details:", error.message);
}
