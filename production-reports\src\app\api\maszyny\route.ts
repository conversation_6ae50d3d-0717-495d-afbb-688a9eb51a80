import { NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";

export async function GET() {
  try {
    const maszyny = await prisma.maszyna.findMany({
      orderBy: {
        nazwa: "asc",
      },
    });

    return NextResponse.json(maszyny);
  } catch (error) {
    console.error("Błąd podczas pobierania maszyn:", error);
    return NextResponse.json(
      { error: "Błąd podczas pobierania maszyn" },
      { status: 500 }
    );
  }
}
