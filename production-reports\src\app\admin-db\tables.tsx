"use client";

import { useState } from "react";

interface TableProps<T> {
  data: T[];
  editingId: number | null;
  onEdit: (id: number | null) => void;
  onSave: (id: number, data: any) => void;
  onDelete: (id: number) => void;
  saving: boolean;
}

export function MaszynyTable({ data, editingId, onEdit, onSave, onDelete, saving }: TableProps<any>) {
  const [editData, setEditData] = useState<any>({});

  const handleEdit = (item: any) => {
    setEditData(item);
    onEdit(item.id);
  };

  const handleSave = () => {
    onSave(editingId!, editData);
  };

  const handleCancel = () => {
    setEditData({});
    onEdit(null);
  };

  return (
    <table className="w-full text-sm text-gray-300">
      <thead>
        <tr className="border-b border-gray-600">
          <th className="text-left p-2">ID</th>
          <th className="text-left p-2">Nazwa</th>
          <th className="text-left p-2">Typ</th>
          <th className="text-left p-2">Info</th>
          <th className="text-left p-2">Akcje</th>
        </tr>
      </thead>
      <tbody>
        {data.map((item) => (
          <tr key={item.id} className="border-b border-gray-700 hover:bg-gray-700">
            <td className="p-2">{item.id}</td>
            <td className="p-2">
              {editingId === item.id ? (
                <input
                  type="text"
                  value={editData.nazwa || ''}
                  onChange={(e) => setEditData({...editData, nazwa: e.target.value})}
                  className="w-full p-1 bg-gray-600 border border-gray-500 rounded text-white text-xs"
                />
              ) : (
                item.nazwa
              )}
            </td>
            <td className="p-2">
              {editingId === item.id ? (
                <input
                  type="text"
                  value={editData.typ || ''}
                  onChange={(e) => setEditData({...editData, typ: e.target.value})}
                  className="w-full p-1 bg-gray-600 border border-gray-500 rounded text-white text-xs"
                />
              ) : (
                item.typ || '-'
              )}
            </td>
            <td className="p-2">
              {editingId === item.id ? (
                <textarea
                  value={editData.info || ''}
                  onChange={(e) => setEditData({...editData, info: e.target.value})}
                  rows={2}
                  className="w-full p-1 bg-gray-600 border border-gray-500 rounded text-white text-xs resize-none"
                />
              ) : (
                item.info || '-'
              )}
            </td>
            <td className="p-2">
              {editingId === item.id ? (
                <div className="flex space-x-1">
                  <button
                    onClick={handleSave}
                    disabled={saving}
                    className="bg-green-600 hover:bg-green-700 disabled:bg-gray-600 text-white px-2 py-1 rounded text-xs"
                  >
                    💾
                  </button>
                  <button
                    onClick={handleCancel}
                    className="bg-gray-600 hover:bg-gray-700 text-white px-2 py-1 rounded text-xs"
                  >
                    ❌
                  </button>
                </div>
              ) : (
                <div className="flex space-x-1">
                  <button
                    onClick={() => handleEdit(item)}
                    className="bg-blue-600 hover:bg-blue-700 text-white px-2 py-1 rounded text-xs"
                  >
                    ✏️
                  </button>
                  <button
                    onClick={() => onDelete(item.id)}
                    className="bg-red-600 hover:bg-red-700 text-white px-2 py-1 rounded text-xs"
                  >
                    🗑️
                  </button>
                </div>
              )}
            </td>
          </tr>
        ))}
      </tbody>
    </table>
  );
}

export function PracownicyTable({ data, editingId, onEdit, onSave, onDelete, saving }: TableProps<any>) {
  const [editData, setEditData] = useState<any>({});

  const handleEdit = (item: any) => {
    setEditData(item);
    onEdit(item.id);
  };

  const handleSave = () => {
    onSave(editingId!, editData);
  };

  const handleCancel = () => {
    setEditData({});
    onEdit(null);
  };

  return (
    <table className="w-full text-sm text-gray-300">
      <thead>
        <tr className="border-b border-gray-600">
          <th className="text-left p-2">ID</th>
          <th className="text-left p-2">Numer</th>
          <th className="text-left p-2">Imię</th>
          <th className="text-left p-2">Nazwisko</th>
          <th className="text-left p-2">Stanowisko</th>
          <th className="text-left p-2">Akcje</th>
        </tr>
      </thead>
      <tbody>
        {data.map((item) => (
          <tr key={item.id} className="border-b border-gray-700 hover:bg-gray-700">
            <td className="p-2">{item.id}</td>
            <td className="p-2">
              {editingId === item.id ? (
                <input
                  type="text"
                  value={editData.numer || ''}
                  onChange={(e) => setEditData({...editData, numer: e.target.value})}
                  className="w-full p-1 bg-gray-600 border border-gray-500 rounded text-white text-xs"
                />
              ) : (
                item.numer
              )}
            </td>
            <td className="p-2">
              {editingId === item.id ? (
                <input
                  type="text"
                  value={editData.imie || ''}
                  onChange={(e) => setEditData({...editData, imie: e.target.value})}
                  className="w-full p-1 bg-gray-600 border border-gray-500 rounded text-white text-xs"
                />
              ) : (
                item.imie || '-'
              )}
            </td>
            <td className="p-2">
              {editingId === item.id ? (
                <input
                  type="text"
                  value={editData.nazwisko || ''}
                  onChange={(e) => setEditData({...editData, nazwisko: e.target.value})}
                  className="w-full p-1 bg-gray-600 border border-gray-500 rounded text-white text-xs"
                />
              ) : (
                item.nazwisko || '-'
              )}
            </td>
            <td className="p-2">
              {editingId === item.id ? (
                <input
                  type="text"
                  value={editData.stanowisko || ''}
                  onChange={(e) => setEditData({...editData, stanowisko: e.target.value})}
                  className="w-full p-1 bg-gray-600 border border-gray-500 rounded text-white text-xs"
                />
              ) : (
                item.stanowisko || '-'
              )}
            </td>
            <td className="p-2">
              {editingId === item.id ? (
                <div className="flex space-x-1">
                  <button
                    onClick={handleSave}
                    disabled={saving}
                    className="bg-green-600 hover:bg-green-700 disabled:bg-gray-600 text-white px-2 py-1 rounded text-xs"
                  >
                    💾
                  </button>
                  <button
                    onClick={handleCancel}
                    className="bg-gray-600 hover:bg-gray-700 text-white px-2 py-1 rounded text-xs"
                  >
                    ❌
                  </button>
                </div>
              ) : (
                <div className="flex space-x-1">
                  <button
                    onClick={() => handleEdit(item)}
                    className="bg-blue-600 hover:bg-blue-700 text-white px-2 py-1 rounded text-xs"
                  >
                    ✏️
                  </button>
                  <button
                    onClick={() => onDelete(item.id)}
                    className="bg-red-600 hover:bg-red-700 text-white px-2 py-1 rounded text-xs"
                  >
                    🗑️
                  </button>
                </div>
              )}
            </td>
          </tr>
        ))}
      </tbody>
    </table>
  );
}

export function SurowceTable({ data, editingId, onEdit, onSave, onDelete, saving }: TableProps<any>) {
  const [editData, setEditData] = useState<any>({});

  const handleEdit = (item: any) => {
    setEditData(item);
    onEdit(item.id);
  };

  const handleSave = () => {
    onSave(editingId!, editData);
  };

  const handleCancel = () => {
    setEditData({});
    onEdit(null);
  };

  return (
    <table className="w-full text-sm text-gray-300">
      <thead>
        <tr className="border-b border-gray-600">
          <th className="text-left p-2">ID</th>
          <th className="text-left p-2">Nazwa</th>
          <th className="text-left p-2">Typ</th>
          <th className="text-left p-2">Info</th>
          <th className="text-left p-2">Akcje</th>
        </tr>
      </thead>
      <tbody>
        {data.map((item) => (
          <tr key={item.id} className="border-b border-gray-700 hover:bg-gray-700">
            <td className="p-2">{item.id}</td>
            <td className="p-2">
              {editingId === item.id ? (
                <input
                  type="text"
                  value={editData.nazwa || ''}
                  onChange={(e) => setEditData({...editData, nazwa: e.target.value})}
                  className="w-full p-1 bg-gray-600 border border-gray-500 rounded text-white text-xs"
                />
              ) : (
                item.nazwa
              )}
            </td>
            <td className="p-2">
              {editingId === item.id ? (
                <input
                  type="text"
                  value={editData.typ || ''}
                  onChange={(e) => setEditData({...editData, typ: e.target.value})}
                  className="w-full p-1 bg-gray-600 border border-gray-500 rounded text-white text-xs"
                />
              ) : (
                item.typ || '-'
              )}
            </td>
            <td className="p-2">
              {editingId === item.id ? (
                <textarea
                  value={editData.info || ''}
                  onChange={(e) => setEditData({...editData, info: e.target.value})}
                  rows={2}
                  className="w-full p-1 bg-gray-600 border border-gray-500 rounded text-white text-xs resize-none"
                />
              ) : (
                item.info || '-'
              )}
            </td>
            <td className="p-2">
              {editingId === item.id ? (
                <div className="flex space-x-1">
                  <button
                    onClick={handleSave}
                    disabled={saving}
                    className="bg-green-600 hover:bg-green-700 disabled:bg-gray-600 text-white px-2 py-1 rounded text-xs"
                  >
                    💾
                  </button>
                  <button
                    onClick={handleCancel}
                    className="bg-gray-600 hover:bg-gray-700 text-white px-2 py-1 rounded text-xs"
                  >
                    ❌
                  </button>
                </div>
              ) : (
                <div className="flex space-x-1">
                  <button
                    onClick={() => handleEdit(item)}
                    className="bg-blue-600 hover:bg-blue-700 text-white px-2 py-1 rounded text-xs"
                  >
                    ✏️
                  </button>
                  <button
                    onClick={() => onDelete(item.id)}
                    className="bg-red-600 hover:bg-red-700 text-white px-2 py-1 rounded text-xs"
                  >
                    🗑️
                  </button>
                </div>
              )}
            </td>
          </tr>
        ))}
      </tbody>
    </table>
  );
}

export function MaterialyTable({ data, editingId, onEdit, onSave, onDelete, saving }: TableProps<any>) {
  const [editData, setEditData] = useState<any>({});

  const handleEdit = (item: any) => {
    setEditData(item);
    onEdit(item.id);
  };

  const handleSave = () => {
    onSave(editingId!, editData);
  };

  const handleCancel = () => {
    setEditData({});
    onEdit(null);
  };

  return (
    <table className="w-full text-sm text-gray-300">
      <thead>
        <tr className="border-b border-gray-600">
          <th className="text-left p-2">ID</th>
          <th className="text-left p-2">Nazwa</th>
          <th className="text-left p-2">Typ</th>
          <th className="text-left p-2">Info</th>
          <th className="text-left p-2">Akcje</th>
        </tr>
      </thead>
      <tbody>
        {data.map((item) => (
          <tr key={item.id} className="border-b border-gray-700 hover:bg-gray-700">
            <td className="p-2">{item.id}</td>
            <td className="p-2">
              {editingId === item.id ? (
                <input
                  type="text"
                  value={editData.nazwa || ''}
                  onChange={(e) => setEditData({...editData, nazwa: e.target.value})}
                  className="w-full p-1 bg-gray-600 border border-gray-500 rounded text-white text-xs"
                />
              ) : (
                item.nazwa
              )}
            </td>
            <td className="p-2">
              {editingId === item.id ? (
                <input
                  type="text"
                  value={editData.typ || ''}
                  onChange={(e) => setEditData({...editData, typ: e.target.value})}
                  className="w-full p-1 bg-gray-600 border border-gray-500 rounded text-white text-xs"
                />
              ) : (
                item.typ || '-'
              )}
            </td>
            <td className="p-2">
              {editingId === item.id ? (
                <textarea
                  value={editData.info || ''}
                  onChange={(e) => setEditData({...editData, info: e.target.value})}
                  rows={2}
                  className="w-full p-1 bg-gray-600 border border-gray-500 rounded text-white text-xs resize-none"
                />
              ) : (
                item.info || '-'
              )}
            </td>
            <td className="p-2">
              {editingId === item.id ? (
                <div className="flex space-x-1">
                  <button
                    onClick={handleSave}
                    disabled={saving}
                    className="bg-green-600 hover:bg-green-700 disabled:bg-gray-600 text-white px-2 py-1 rounded text-xs"
                  >
                    💾
                  </button>
                  <button
                    onClick={handleCancel}
                    className="bg-gray-600 hover:bg-gray-700 text-white px-2 py-1 rounded text-xs"
                  >
                    ❌
                  </button>
                </div>
              ) : (
                <div className="flex space-x-1">
                  <button
                    onClick={() => handleEdit(item)}
                    className="bg-blue-600 hover:bg-blue-700 text-white px-2 py-1 rounded text-xs"
                  >
                    ✏️
                  </button>
                  <button
                    onClick={() => onDelete(item.id)}
                    className="bg-red-600 hover:bg-red-700 text-white px-2 py-1 rounded text-xs"
                  >
                    🗑️
                  </button>
                </div>
              )}
            </td>
          </tr>
        ))}
      </tbody>
    </table>
  );
}
