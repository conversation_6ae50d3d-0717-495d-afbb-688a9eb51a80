const { PrismaClient } = require("@prisma/client");

const prisma = new PrismaClient();

async function main() {
  // Dodaj przykładowe maszyny
  const maszyny = await Promise.all([
    prisma.maszyna.create({
      data: {
        nazwa: "Maszyna A1",
        typ: "Produkcyjna",
        info: "Główna maszyna produkcyjna",
      },
    }),
    prisma.maszyna.create({
      data: {
        nazwa: "Maszyna B2",
        typ: "Pomocnicza",
        info: "Maszyna pomocnicza",
      },
    }),
    prisma.maszyna.create({
      data: {
        nazwa: "Maszyna C3",
        typ: "Produkcyjna",
        info: "Druga maszyna produkcyjna",
      },
    }),
  ]);

  // Dodaj przykładowych pracowników
  const pracownicy = await Promise.all([
    prisma.pracownik.create({
      data: {
        numer: "001",
        imie: "<PERSON>",
        nazwisko: "<PERSON><PERSON><PERSON>",
        stanowisko: "Operator",
      },
    }),
    prisma.pracownik.create({
      data: {
        numer: "002",
        imie: "<PERSON>",
        nazwisko: "Nowak",
        stanowisko: "Operator",
      },
    }),
    prisma.pracownik.create({
      data: {
        numer: "003",
        imie: "Piotr",
        nazwisko: "Wiśniewski",
        stanowisko: "Brygadzista",
      },
    }),
  ]);

  // Dodaj przykładowe surowce
  const surowce = await Promise.all([
    prisma.surowiec.create({
      data: {
        nazwa: "Elastlok 33M",
        typ: "Podstawowy",
        info: "Główny surowiec produkcyjny",
      },
    }),
    prisma.surowiec.create({
      data: {
        nazwa: "Elastlok 25M",
        typ: "Podstawowy",
        info: "Alternatywny surowiec",
      },
    }),
    prisma.surowiec.create({
      data: {
        nazwa: "Plastik PVC",
        typ: "Pomocniczy",
        info: "Surowiec pomocniczy",
      },
    }),
  ]);

  // Dodaj przykładowe materiały
  const materialy = await Promise.all([
    prisma.material.create({
      data: {
        nazwa: "FG24 160mm",
        typ: "Wykończeniowy",
        info: "Materiał wykończeniowy",
      },
    }),
    prisma.material.create({
      data: {
        nazwa: "FG18 120mm",
        typ: "Wykończeniowy",
        info: "Materiał wykończeniowy mniejszy",
      },
    }),
    prisma.material.create({
      data: {
        nazwa: "Klej przemysłowy",
        typ: "Pomocniczy",
        info: "Klej do łączenia",
      },
    }),
  ]);

  console.log("Seedowanie zakończone!");
  console.log(`Dodano ${maszyny.length} maszyn`);
  console.log(`Dodano ${pracownicy.length} pracowników`);
  console.log(`Dodano ${surowce.length} surowców`);
  console.log(`Dodano ${materialy.length} materiałów`);
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
