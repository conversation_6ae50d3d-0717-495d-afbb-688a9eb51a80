import { NextResponse } from "next/server";

export async function GET() {
  try {
    const Database = require("better-sqlite3");
    const path = require("path");

    const dbPath = path.join(process.cwd(), "prisma", "baza_danych.db");
    const db = new Database(dbPath, { readonly: true });

    const produkty = db
      .prepare("SELECT * FROM produkty ORDER BY kod_handlowy ASC")
      .all();

    db.close();

    return NextResponse.json(produkty);
  } catch (error) {
    console.error("Direct database error:", error);
    const errorMessage =
      error instanceof Error ? error.message : "An unknown error occurred";
    return NextResponse.json(
      { error: "Database error: " + errorMessage },
      { status: 500 }
    );
  }
}
