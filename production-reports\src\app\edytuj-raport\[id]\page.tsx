"use client";

import { useState, useEffect, useMemo } from "react";
import Link from "next/link";
import { use<PERSON>ara<PERSON>, useRouter } from "next/navigation";

// Types
interface Produkt {
  id: number;
  kod_handlowy: string;
  nazwa: string;
  wydajnosc: number;
  material_1?: string;
  material_1_percent?: number;
  material_2?: string;
  material_2_percent?: number;
  material_3?: string;
  material_3_percent?: number;
  material_4?: string;
  material_4_percent?: number;
  material_5?: string;
  material_5_percent?: number;
}

interface Maszyna {
  id: number;
  nazwa: string;
}

interface Pracownik {
  id: number;
  numer: string;
  imie: string;
  nazwisko: string;
}

interface Surowiec {
  id: number;
  nazwa: string;
  waga: number;
}

interface Material {
  id: number;
  nazwa: string;
  material_percent: number;
}

interface SurowiecEntry {
  percentage: string;
  surowiec_id: string;
  calculated_usage: number;
  szarza: string;
  zuzycie_surowca: string;
  odpad_nieuzytkowy: string;
}

interface MetrazEntry {
  metraz: string;
  ilosc: string;
}

interface MaterialEntry {
  percentage: string;
  material_id: string;
  calculated_usage: number;
  status: string;
  zuzycie: string;
  odpad: string;
}

interface CzasPracy {
  czas_pracy_maszyny: string;
  czas_pracy_pracownika: string;
  czas_rozgrzania: string;
  czas_dogrzania: string;
  czas_przebudowa: string;
  czas_inne: string;
}

interface FormData {
  produkt_id: string;
  nr_zamowienia: string;
  waga: string;
  maszyna_id: string;
  pracownik_id: string;
  uwagi: string;
}

interface RaportProdukcyjny {
  id: number;
  data_utworzenia: string;
  produkt_id: number;
  metraz_rolek: number;
  ilosc_rolek: number;
  nr_zamowienia?: string;
  waga?: number;
  surowiec_id: number;
  zuzyty_surowiec: number;
  odpad_surowiec: number;
  szarza_surowca?: string;
  material_id: number;
  zuzyty_material: number;
  odpad_material: number;
  maszyna_id: number;
  pracownik_id: number;
  czas_pracy_maszyny: number;
  czas_pracy_pracownika: number;
  uwagi?: string;
  produkt_kod?: string;
  produkt_nazwa?: string;
  maszyna_nazwa?: string;
  pracownik_imie?: string;
  pracownik_nazwisko?: string;
  surowiec_nazwa?: string;
  material_nazwa?: string;
}

export default function EdytujRaportPage() {
  const params = useParams();
  const router = useRouter();
  const reportId = params.id as string;

  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [raport, setRaport] = useState<RaportProdukcyjny | null>(null);

  // Master data
  const [produkty, setProdukty] = useState<Produkt[]>([]);
  const [maszyny, setMaszyny] = useState<Maszyna[]>([]);
  const [pracownicy, setPracownicy] = useState<Pracownik[]>([]);
  const [surowce, setSurowce] = useState<Surowiec[]>([]);
  const [materialy, setMaterialy] = useState<Material[]>([]);

  // Form state
  const [formData, setFormData] = useState<FormData>({
    produkt_id: "",
    nr_zamowienia: "",
    waga: "",
    maszyna_id: "",
    pracownik_id: "",
    uwagi: "",
  });

  const [surowceEntries, setSurowceEntries] = useState<SurowiecEntry[]>([
    {
      percentage: "100",
      surowiec_id: "",
      calculated_usage: 0,
      szarza: "",
      zuzycie_surowca: "",
      odpad_nieuzytkowy: "",
    },
    {
      percentage: "",
      surowiec_id: "",
      calculated_usage: 0,
      szarza: "",
      zuzycie_surowca: "",
      odpad_nieuzytkowy: "",
    },
    {
      percentage: "",
      surowiec_id: "",
      calculated_usage: 0,
      szarza: "",
      zuzycie_surowca: "",
      odpad_nieuzytkowy: "",
    },
    {
      percentage: "",
      surowiec_id: "",
      calculated_usage: 0,
      szarza: "",
      zuzycie_surowca: "",
      odpad_nieuzytkowy: "",
    },
    {
      percentage: "",
      surowiec_id: "",
      calculated_usage: 0,
      szarza: "",
      zuzycie_surowca: "",
      odpad_nieuzytkowy: "",
    },
  ]);

  const [metrazEntries, setMetrazEntries] = useState<MetrazEntry[]>([
    { metraz: "", ilosc: "" },
    { metraz: "", ilosc: "" },
    { metraz: "", ilosc: "" },
    { metraz: "", ilosc: "" },
    { metraz: "", ilosc: "" },
  ]);

  const [materialyEntries, setMaterialyEntries] = useState<MaterialEntry[]>([
    {
      percentage: "",
      material_id: "",
      calculated_usage: 0,
      status: "",
      zuzycie: "",
      odpad: "",
    },
    {
      percentage: "",
      material_id: "",
      calculated_usage: 0,
      status: "",
      zuzycie: "",
      odpad: "",
    },
    {
      percentage: "",
      material_id: "",
      calculated_usage: 0,
      status: "",
      zuzycie: "",
      odpad: "",
    },
    {
      percentage: "",
      material_id: "",
      calculated_usage: 0,
      status: "",
      zuzycie: "",
      odpad: "",
    },
    {
      percentage: "",
      material_id: "",
      calculated_usage: 0,
      status: "",
      zuzycie: "",
      odpad: "",
    },
  ]);

  const [czasPracy, setCzasPracy] = useState<CzasPracy>({
    czas_pracy_maszyny: "",
    czas_pracy_pracownika: "",
    czas_rozgrzania: "",
    czas_dogrzania: "",
    czas_przebudowa: "",
    czas_inne: "",
  });

  useEffect(() => {
    fetchData();
    fetchReport();
  }, [reportId]);

  const fetchData = async () => {
    try {
      const [produktyRes, maszynyRes, pracownicyRes, surowceRes, materialyRes] =
        await Promise.all([
          fetch("/api/produkty-direct"),
          fetch("/api/maszyny"),
          fetch("/api/pracownicy"),
          fetch("/api/surowce"),
          fetch("/api/materialy"),
        ]);

      const produktyData = await produktyRes.json();
      setProdukty(produktyData);
      setMaszyny(await maszynyRes.json());
      setPracownicy(await pracownicyRes.json());
      setSurowce(await surowceRes.json());
      setMaterialy(await materialyRes.json());
    } catch (error) {
      console.error("Błąd podczas ładowania danych:", error);
    }
  };

  const fetchReport = async () => {
    try {
      const response = await fetch(`/api/raporty-direct/${reportId}`);
      if (response.ok) {
        const reportData = await response.json();
        setRaport(reportData);
        populateFormWithReportData(reportData);
      } else {
        console.error("Błąd podczas ładowania raportu");
        router.push("/raporty");
      }
    } catch (error) {
      console.error("Błąd podczas ładowania raportu:", error);
      router.push("/raporty");
    } finally {
      setLoading(false);
    }
  };

  const populateFormWithReportData = (reportData: RaportProdukcyjny) => {
    // Populate basic form data
    setFormData({
      produkt_id: reportData.produkt_id.toString(),
      nr_zamowienia: reportData.nr_zamowienia || "",
      waga: reportData.waga?.toString() || "",
      maszyna_id: reportData.maszyna_id.toString(),
      pracownik_id: reportData.pracownik_id.toString(),
      uwagi: reportData.uwagi || "",
    });

    // Populate metraz entries
    const newMetrazEntries = [...metrazEntries];
    newMetrazEntries[0] = {
      metraz: reportData.metraz_rolek.toString(),
      ilosc: reportData.ilosc_rolek.toString(),
    };
    setMetrazEntries(newMetrazEntries);

    // Populate surowiec entries
    const newSurowceEntries = [...surowceEntries];
    newSurowceEntries[0] = {
      percentage: "100", // Default to 100%
      surowiec_id: reportData.surowiec_id.toString(),
      calculated_usage: 0,
      szarza: reportData.szarza_surowca || "",
      zuzycie_surowca: reportData.zuzyty_surowiec.toString(),
      odpad_nieuzytkowy: reportData.odpad_surowiec.toString(),
    };
    setSurowceEntries(newSurowceEntries);

    // Populate material entries
    const newMaterialyEntries = [...materialyEntries];
    newMaterialyEntries[0] = {
      percentage: "", // Will be loaded from database
      material_id: reportData.material_id.toString(),
      calculated_usage: 0,
      status: "", // Will be loaded from database
      zuzycie: reportData.zuzyty_material.toString(),
      odpad: reportData.odpad_material.toString(),
    };
    setMaterialyEntries(newMaterialyEntries);

    // Populate time entries
    setCzasPracy({
      czas_pracy_maszyny: reportData.czas_pracy_maszyny.toString(),
      czas_pracy_pracownika: reportData.czas_pracy_pracownika.toString(),
      czas_rozgrzania: "",
      czas_dogrzania: "",
      czas_przebudowa: "",
      czas_inne: "",
    });
  };

  const handleChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement
    >
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleMetrazChange = (
    index: number,
    field: "metraz" | "ilosc",
    value: string
  ) => {
    const newEntries = [...metrazEntries];
    newEntries[index] = { ...newEntries[index], [field]: value };
    setMetrazEntries(newEntries);
  };

  const calculateTotalMeters = (): number => {
    return metrazEntries.reduce((sum, entry) => {
      const metraz = parseFloat(entry.metraz) || 0;
      const ilosc = parseFloat(entry.ilosc) || 0;
      return sum + metraz * ilosc;
    }, 0);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSubmitting(true);

    try {
      // Prepare the data for submission
      const reportData = {
        produkt_id: parseInt(formData.produkt_id),
        metraz_rolek: parseFloat(metrazEntries[0]?.metraz || "0"),
        ilosc_rolek: parseInt(metrazEntries[0]?.ilosc || "0"),
        nr_zamowienia: formData.nr_zamowienia,
        waga: parseFloat(formData.waga) || null,
        surowiec_id: parseInt(surowceEntries[0]?.surowiec_id || "0"),
        zuzyty_surowiec: parseFloat(surowceEntries[0]?.zuzycie_surowca || "0"),
        odpad_surowiec: parseFloat(surowceEntries[0]?.odpad_nieuzytkowy || "0"),
        szarza_surowca: surowceEntries[0]?.szarza || null,
        material_id: parseInt(materialyEntries[0]?.material_id || "0"),
        zuzyty_material: parseFloat(materialyEntries[0]?.zuzycie || "0"),
        odpad_material: parseFloat(materialyEntries[0]?.odpad || "0"),
        maszyna_id: parseInt(formData.maszyna_id),
        pracownik_id: parseInt(formData.pracownik_id),
        czas_pracy_maszyny: parseFloat(czasPracy.czas_pracy_maszyny),
        czas_pracy_pracownika: parseFloat(czasPracy.czas_pracy_pracownika),
        uwagi: formData.uwagi,
      };

      const response = await fetch(`/api/raporty-direct/${reportId}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(reportData),
      });

      if (response.ok) {
        alert("Raport został zaktualizowany pomyślnie!");
        router.push("/raporty");
      } else {
        const errorData = await response.json();
        alert(`Błąd podczas aktualizacji raportu: ${errorData.error}`);
      }
    } catch (error) {
      console.error("Błąd:", error);
      alert("Wystąpił błąd podczas aktualizacji raportu.");
    } finally {
      setSubmitting(false);
    }
  };

  const totalMeters = useMemo(() => calculateTotalMeters(), [metrazEntries]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 flex items-center justify-center">
        <div className="text-white text-xl">Ładowanie...</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 p-4">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="glass-dark rounded-t-2xl border-x border-t border-gray-600/50 p-6 animate-fade-in-down">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="relative">
                <div className="text-4xl floating">📝</div>
                <div className="absolute inset-0 bg-blue-500/20 rounded-full blur-xl"></div>
              </div>
              <div>
                <h1 className="text-3xl font-bold text-white mb-2">
                  Edytuj Raport Produkcyjny
                </h1>
                <p className="text-gray-300">
                  Raport ID: {reportId} | {raport?.produkt_kod} -{" "}
                  {raport?.produkt_nazwa}
                </p>
              </div>
            </div>
            <Link
              href="/raporty"
              className="btn-modern glass border border-gray-600/30 hover:border-blue-500/50 text-white py-2 px-4 rounded-xl transition-all duration-300 font-medium"
            >
              ← Powrót do listy
            </Link>
          </div>
        </div>

        {/* Form Content */}
        <form
          onSubmit={handleSubmit}
          className="glass-dark border-x border-b border-gray-600/50 rounded-b-2xl p-6"
        >
          <div className="grid grid-cols-12 gap-6">
            {/* Left Column - Product and Basic Info */}
            <div className="col-span-4 space-y-6">
              {/* Product Selection */}
              <div className="bg-gray-800/50 rounded-xl p-4 border border-gray-600/30">
                <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
                  <span className="w-2 h-2 bg-blue-400 rounded-full"></span>
                  <span>Produkt</span>
                </h3>
                <div className="space-y-3">
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Kod handlowy
                    </label>
                    <select
                      name="produkt_id"
                      value={formData.produkt_id}
                      onChange={handleChange}
                      className="w-full p-3 bg-gray-700 border border-gray-600 rounded-xl text-white focus:ring-2 focus:ring-blue-400 focus:border-blue-400 transition-all duration-300"
                      required
                    >
                      <option value="">Wybierz produkt</option>
                      {produkty.map((produkt) => (
                        <option key={produkt.id} value={produkt.id}>
                          {produkt.kod_handlowy}
                        </option>
                      ))}
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Nazwa produktu
                    </label>
                    <input
                      type="text"
                      value={
                        produkty.find(
                          (p) => p.id === parseInt(formData.produkt_id)
                        )?.nazwa || ""
                      }
                      readOnly
                      className="w-full p-3 bg-gray-600 border border-gray-600 rounded-xl text-white cursor-not-allowed"
                      placeholder="Automatycznie wypełniane..."
                    />
                  </div>
                </div>
              </div>

              {/* Order and Weight */}
              <div className="bg-gray-800/50 rounded-xl p-4 border border-gray-600/30">
                <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
                  <span className="w-2 h-2 bg-green-400 rounded-full"></span>
                  <span>Zamówienie</span>
                </h3>
                <div className="space-y-3">
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Nr zamówienia
                    </label>
                    <input
                      type="text"
                      name="nr_zamowienia"
                      value={formData.nr_zamowienia}
                      onChange={handleChange}
                      className="w-full p-3 bg-gray-700 border border-gray-600 rounded-xl text-white focus:ring-2 focus:ring-blue-400 focus:border-blue-400 transition-all duration-300"
                      placeholder="Wprowadź nr zamówienia..."
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Waga [g]
                    </label>
                    <input
                      type="number"
                      step="0.1"
                      name="waga"
                      value={formData.waga}
                      onChange={handleChange}
                      className="w-full p-3 bg-gray-700 border border-gray-600 rounded-xl text-white focus:ring-2 focus:ring-blue-400 focus:border-blue-400 transition-all duration-300"
                      placeholder="0.0"
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* Middle Column - Metraz */}
            <div className="col-span-4 space-y-6">
              <div className="bg-gray-800/50 rounded-xl p-4 border border-gray-600/30">
                <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
                  <span className="w-2 h-2 bg-purple-400 rounded-full"></span>
                  <span>Metraż rolek</span>
                </h3>
                <div className="space-y-2">
                  <div className="grid grid-cols-12 gap-2 text-xs text-gray-400 font-medium">
                    <div className="col-span-6">Metraż [m]</div>
                    <div className="col-span-6">Ilość [szt]</div>
                  </div>
                  {metrazEntries.map((entry, index) => (
                    <div key={index} className="grid grid-cols-12 gap-2">
                      <div className="col-span-6">
                        <input
                          type="number"
                          step="0.1"
                          value={entry.metraz}
                          onChange={(e) =>
                            handleMetrazChange(index, "metraz", e.target.value)
                          }
                          className="w-full p-2 bg-gray-700 border border-gray-600 rounded text-white text-sm focus:ring-1 focus:ring-blue-500"
                          placeholder="0"
                        />
                      </div>
                      <div className="col-span-6">
                        <input
                          type="number"
                          value={entry.ilosc}
                          onChange={(e) =>
                            handleMetrazChange(index, "ilosc", e.target.value)
                          }
                          className="w-full p-2 bg-gray-700 border border-gray-600 rounded text-white text-sm focus:ring-1 focus:ring-blue-500"
                          placeholder="0"
                        />
                      </div>
                    </div>
                  ))}
                  <div className="mt-3 p-2 bg-gray-700 rounded border border-gray-600">
                    <div className="text-sm text-gray-300">
                      Suma metrów:{" "}
                      <span className="text-white font-semibold">
                        {totalMeters.toFixed(1)} m
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Right Column - Machine and Worker */}
            <div className="col-span-4 space-y-6">
              <div className="bg-gray-800/50 rounded-xl p-4 border border-gray-600/30">
                <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
                  <span className="w-2 h-2 bg-orange-400 rounded-full"></span>
                  <span>Maszyna i Pracownik</span>
                </h3>
                <div className="space-y-3">
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Maszyna
                    </label>
                    <select
                      name="maszyna_id"
                      value={formData.maszyna_id}
                      onChange={handleChange}
                      className="w-full p-3 bg-gray-700 border border-gray-600 rounded-xl text-white focus:ring-2 focus:ring-blue-400 focus:border-blue-400 transition-all duration-300"
                      required
                    >
                      <option value="">Wybierz maszynę</option>
                      {maszyny.map((maszyna) => (
                        <option key={maszyna.id} value={maszyna.id}>
                          {maszyna.nazwa}
                        </option>
                      ))}
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Pracownik
                    </label>
                    <select
                      name="pracownik_id"
                      value={formData.pracownik_id}
                      onChange={handleChange}
                      className="w-full p-3 bg-gray-700 border border-gray-600 rounded-xl text-white focus:ring-2 focus:ring-blue-400 focus:border-blue-400 transition-all duration-300"
                      required
                    >
                      <option value="">Wybierz pracownika</option>
                      {pracownicy.map((pracownik) => (
                        <option key={pracownik.id} value={pracownik.id}>
                          {pracownik.numer} - {pracownik.imie}{" "}
                          {pracownik.nazwisko}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="mt-8 flex justify-between items-center">
            <Link
              href="/raporty"
              className="btn-modern glass border border-gray-600/30 hover:border-red-500/50 text-white py-3 px-6 rounded-xl transition-all duration-300 font-medium"
            >
              Anuluj
            </Link>
            <button
              type="submit"
              disabled={submitting}
              className="btn-modern bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white py-3 px-8 rounded-xl transition-all duration-300 font-medium disabled:opacity-50"
            >
              {submitting ? "Zapisywanie..." : "Zapisz zmiany"}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
