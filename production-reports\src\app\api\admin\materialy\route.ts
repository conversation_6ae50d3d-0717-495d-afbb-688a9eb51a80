import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";

export async function GET() {
  try {
    const materialy = await prisma.material.findMany({
      orderBy: {
        nazwa: "asc",
      },
    });

    return NextResponse.json(materialy);
  } catch (error) {
    console.error("Błąd podczas pobierania materiałów:", error);
    return NextResponse.json(
      { error: "Błąd podczas pobierania materiałów" },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const data = await request.json();

    const material = await prisma.material.create({
      data: {
        nazwa: data.nazwa,
        typ: data.typ || null,
        info: data.info || null,
      },
    });

    return NextResponse.json(material);
  } catch (error) {
    console.error("Błąd podczas dodawania materiału:", error);
    return NextResponse.json(
      { error: "Błąd podczas dodawania materiału" },
      { status: 500 }
    );
  }
}
