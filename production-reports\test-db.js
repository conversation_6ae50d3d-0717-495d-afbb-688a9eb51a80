try {
  const Database = require("better-sqlite3");
  const path = require("path");

  const dbPath = path.join(__dirname, "baza_danych.db");
  console.log("Database path:", dbPath);
  
  const db = new Database(dbPath);

  console.log("=== Database Schema ===");
  const tableInfo = db.prepare("PRAGMA table_info(raporty_produkcyjne)").all();
  console.log("Columns in raporty_produkcyjne:");
  tableInfo.forEach((col) => {
    console.log(`- ${col.name}: ${col.type} (nullable: ${col.notnull === 0})`);
  });

  console.log("\n=== Recent Reports ===");
  const reports = db.prepare(`
    SELECT 
      r.*,
      p.kod_handlowy,
      p.nazwa as produkt_nazwa
    FROM raporty_produkcyjne r
    LEFT JOIN produkty p ON r.produkt_id = p.id
    ORDER BY r.id DESC 
    LIMIT 5
  `).all();

  console.log(`Found ${reports.length} reports`);
  
  reports.forEach((report) => {
    console.log(`\n=== Report ID: ${report.id} ===`);
    console.log(`Product: ${report.kod_handlowy} - ${report.produkt_nazwa}`);
    console.log(`Metraz rolek: ${report.metraz_rolek}`);
    console.log(`Ilosc rolek: ${report.ilosc_rolek}`);
    console.log(`Calculated total meters: ${report.metraz_rolek * report.ilosc_rolek}`);
    console.log(`Nr zamowienia: ${report.nr_zamowienia || 'NULL'}`);
    console.log(`Waga: ${report.waga || 'NULL'}`);
    console.log(`Surowiec ID: ${report.surowiec_id || 'NULL'}`);
    console.log(`Zuzyty surowiec: ${report.zuzyty_surowiec || 'NULL'}`);
    console.log(`Odpad surowiec: ${report.odpad_surowiec || 'NULL'}`);
    console.log(`Szarza surowca: ${report.szarza_surowca || 'NULL'}`);
    console.log(`Material ID: ${report.material_id || 'NULL'}`);
    console.log(`Zuzyty material: ${report.zuzyty_material || 'NULL'}`);
    console.log(`Odpad material: ${report.odpad_material || 'NULL'}`);
    console.log(`Maszyna ID: ${report.maszyna_id || 'NULL'}`);
    console.log(`Pracownik ID: ${report.pracownik_id || 'NULL'}`);
    console.log(`Czas pracy maszyny: ${report.czas_pracy_maszyny || 'NULL'}`);
    console.log(`Czas pracy pracownika: ${report.czas_pracy_pracownika || 'NULL'}`);
    console.log(`Created: ${report.data_utworzenia}`);
  });

  // Test what the raporty page would see
  console.log("\n=== What Raporty Page Sees ===");
  const raportyData = db.prepare(`
    SELECT
      r.*,
      p.kod_handlowy as produkt_kod,
      p.nazwa as produkt_nazwa,
      m.nazwa as maszyna_nazwa,
      pr.imie as pracownik_imie,
      pr.nazwisko as pracownik_nazwisko,
      s.nazwa as surowiec_nazwa,
      mat.nazwa as material_nazwa
    FROM raporty_produkcyjne r
    LEFT JOIN produkty p ON r.produkt_id = p.id
    LEFT JOIN maszyny m ON r.maszyna_id = m.id
    LEFT JOIN pracownicy pr ON r.pracownik_id = pr.id
    LEFT JOIN surowce s ON r.surowiec_id = s.id
    LEFT JOIN materialy mat ON r.material_id = mat.id
    ORDER BY r.data_utworzenia DESC
    LIMIT 3
  `).all();

  raportyData.forEach((raport) => {
    console.log(`\nRaport ${raport.id}:`);
    console.log(`- Kod handlowy: ${raport.produkt_kod || 'NULL'}`);
    console.log(`- Nazwa: ${raport.produkt_nazwa || 'NULL'}`);
    console.log(`- Nr zamówienia: ${raport.nr_zamowienia || 'NULL'}`);
    console.log(`- Waga: ${raport.waga || 'NULL'}`);
    console.log(`- Metraz rolek: ${raport.metraz_rolek}`);
    console.log(`- Ilosc rolek: ${raport.ilosc_rolek}`);
    console.log(`- Suma metrów: ${(raport.metraz_rolek * raport.ilosc_rolek).toFixed(0)}`);
    console.log(`- Surowiec: ${raport.surowiec_nazwa || 'NULL'}`);
    console.log(`- Zużycie surowca: ${raport.zuzyty_surowiec || 'NULL'}`);
    console.log(`- Odpad nieużytkowy: ${raport.odpad_surowiec || 'NULL'}`);
    console.log(`- Maszyna: ${raport.maszyna_nazwa || 'NULL'}`);
    console.log(`- Pracownik: ${raport.pracownik_imie} ${raport.pracownik_nazwisko}`.trim() || 'NULL');
    console.log(`- Czas pracy: ${raport.czas_pracy_maszyny || 'NULL'}`);
  });

  db.close();
  console.log("\nDatabase check completed successfully!");
} catch (error) {
  console.error("Error:", error.message);
  console.error("Stack:", error.stack);
}
