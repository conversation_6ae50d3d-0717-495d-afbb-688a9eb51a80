import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";

export async function GET() {
  try {
    const pracownicy = await prisma.pracownik.findMany({
      orderBy: {
        numer: "asc",
      },
    });

    return NextResponse.json(pracownicy);
  } catch (error) {
    console.error("Błąd podczas pobierania pracowników:", error);
    return NextResponse.json(
      { error: "Błąd podczas pobierania pracowników" },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const data = await request.json();

    const pracownik = await prisma.pracownik.create({
      data: {
        numer: data.numer,
        imie: data.imie || null,
        nazwisko: data.nazwisko || null,
        stanowisko: data.stanowisko || null,
      },
    });

    return NextResponse.json(pracownik);
  } catch (error) {
    console.error("Błąd podczas dodawania pracownika:", error);
    return NextResponse.json(
      { error: "Błąd podczas dodawania pracownika" },
      { status: 500 }
    );
  }
}
