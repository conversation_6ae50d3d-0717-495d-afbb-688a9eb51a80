@import "tailwindcss";

body {
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
  color: #ededed;
  font-family: "Inter", "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
  overflow-x: hidden;
}

/* Custom gray-750 color for better contrast */
.bg-gray-750 {
  background-color: #374151;
}

.hover\:bg-gray-750:hover {
  background-color: #374151;
}

/* Modern animations and effects */
@keyframes slideInFromTop {
  0% {
    transform: translateY(-100%);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideInFromRight {
  0% {
    transform: translateX(100%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes fadeInUp {
  0% {
    transform: translateY(30px);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

@keyframes glow {
  0%,
  100% {
    box-shadow: 0 0 5px rgba(59, 130, 246, 0.5);
  }
  50% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.8),
      0 0 30px rgba(59, 130, 246, 0.6);
  }
}

.animate-slide-in-top {
  animation: slideInFromTop 0.6s ease-out;
}

.animate-slide-in-right {
  animation: slideInFromRight 0.8s ease-out;
}

.animate-fade-in-up {
  animation: fadeInUp 0.5s ease-out;
}

.animate-pulse-slow {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-shimmer {
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent
  );
  background-size: 200px 100%;
  animation: shimmer 2s infinite;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite alternate;
}

/* Glass morphism effect */
.glass {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.glass-dark {
  background: rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Gradient borders */
.gradient-border {
  position: relative;
  background: linear-gradient(45deg, #1e293b, #334155);
  border-radius: 12px;
}

.gradient-border::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 12px;
  padding: 2px;
  background: linear-gradient(45deg, #3b82f6, #8b5cf6, #06b6d4);
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: exclude;
  -webkit-mask-composite: xor;
}

/* Hover effects for table rows */
.table-row-hover {
  transition: all 0.3s ease;
  position: relative;
}

.table-row-hover:hover {
  background: linear-gradient(
    90deg,
    rgba(59, 130, 246, 0.1),
    rgba(139, 92, 246, 0.1)
  );
  transform: translateX(4px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.table-row-hover:hover::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: linear-gradient(180deg, #3b82f6, #8b5cf6);
  border-radius: 0 4px 4px 0;
}

/* Button hover effects */
.btn-modern {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.btn-modern::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s;
}

.btn-modern:hover::before {
  left: 100%;
}

.btn-modern:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

/* Loading skeleton */
.skeleton {
  background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

/* Floating elements */
.floating {
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* Custom scrollbar */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: rgba(55, 65, 81, 0.3);
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, #3b82f6, #8b5cf6);
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, #2563eb, #7c3aed);
}

/* Table header gradient */
.table-header {
  background: linear-gradient(135deg, #374151 0%, #4b5563 100%);
}

/* Responsive table */
@media (max-width: 768px) {
  .table-container {
    font-size: 0.75rem;
  }

  .sidebar-mobile {
    width: 100%;
    border-left: none;
    border-top: 1px solid rgba(75, 85, 99, 0.5);
  }
}

/* Enhanced focus states */
input:focus,
select:focus {
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Dark dropdown options styling */
select {
  color-scheme: dark;
}

select option {
  background-color: #374151 !important;
  color: #ffffff !important;
  border: none !important;
}

select option:hover {
  background-color: #4b5563 !important;
  color: #ffffff !important;
}

select option:checked {
  background-color: #3b82f6 !important;
  color: #ffffff !important;
}

/* Firefox specific dropdown styling */
@-moz-document url-prefix() {
  select {
    background-color: #374151 !important;
    color: #ffffff !important;
  }

  select option {
    background-color: #374151 !important;
    color: #ffffff !important;
  }

  select option:hover {
    background-color: #4b5563 !important;
  }
}

/* Smooth page transitions */
.page-transition {
  animation: pageSlideIn 0.5s ease-out;
}

@keyframes pageSlideIn {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}
