{"name": "production-reports", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "db:seed": "tsx prisma/seed.ts", "db:update": "node add-columns.js"}, "prisma": {"seed": "tsx prisma/seed.ts"}, "dependencies": {"@prisma/client": "^6.9.0", "better-sqlite3": "^11.10.0", "next": "15.3.3", "prisma": "^6.9.0", "react": "^19.0.0", "react-dom": "^19.0.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/better-sqlite3": "^7.6.13", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.3", "tailwindcss": "^4", "tsx": "^4.19.4", "typescript": "^5"}}