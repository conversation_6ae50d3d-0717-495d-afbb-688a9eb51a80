import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

export default async function TestPage() {
  let produkty = []
  let maszyny = []
  let error = null

  try {
    // Test połączenia
    await prisma.$connect()
    
    // Pobierz produkty
    produkty = await prisma.produkty.findMany()
    
    // Pobierz maszyny
    maszyny = await prisma.maszyna.findMany()
    
  } catch (err) {
    error = err.message
  } finally {
    await prisma.$disconnect()
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow-md p-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-8">Test Bazy Danych</h1>
          
          {error && (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
              <strong>Błąd:</strong> {error}
            </div>
          )}
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div>
              <h2 className="text-xl font-semibold mb-4">Produkty ({produkty.length})</h2>
              <div className="bg-gray-100 p-4 rounded">
                <pre className="text-sm overflow-auto">
                  {JSON.stringify(produkty, null, 2)}
                </pre>
              </div>
            </div>
            
            <div>
              <h2 className="text-xl font-semibold mb-4">Maszyny ({maszyny.length})</h2>
              <div className="bg-gray-100 p-4 rounded">
                <pre className="text-sm overflow-auto">
                  {JSON.stringify(maszyny, null, 2)}
                </pre>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
