"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import {
  ProduktyForm,
  MaszynyForm,
  PracownicyForm,
  SurowceForm,
  MaterialyForm,
  ProduktyTable,
} from "./components";
import {
  MaszynyTable,
  PracownicyTable,
  SurowceTable,
  MaterialyTable,
} from "./tables";

interface Produkt {
  id: number;
  kod_handlowy: string | null;
  nazwa: string | null;
  metraz: number;
  surowiec: string | null;
  material: string | null;
  material_percent: number | null;
  material1: string | null;
  material1_percent: number | null;
  material2: string | null;
  material2_percent: number | null;
  material3: string | null;
  material3_percent: number | null;
  material4: string | null;
  material4_percent: number | null;
  material5: string | null;
  material5_percent: number | null;
  info: string | null;
  wydajnosc: number | null;
}

interface Maszyna {
  id: number;
  nazwa: string;
  typ: string | null;
  info: string | null;
}

interface Pracownik {
  id: number;
  numer: string;
  imie: string | null;
  nazwisko: string | null;
  stanowisko: string | null;
}

interface Surowiec {
  id: number;
  nazwa: string;
  typ: string | null;
  info: string | null;
}

interface Material {
  id: number;
  nazwa: string;
  typ: string | null;
  info: string | null;
}

type TabType = "produkty" | "maszyny" | "pracownicy" | "surowce" | "materialy";

export default function AdminDB() {
  const [activeTab, setActiveTab] = useState<TabType>("produkty");
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);

  // Data states
  const [produkty, setProdukty] = useState<Produkt[]>([]);
  const [maszyny, setMaszyny] = useState<Maszyna[]>([]);
  const [pracownicy, setPracownicy] = useState<Pracownik[]>([]);
  const [surowce, setSurowce] = useState<Surowiec[]>([]);
  const [materialy, setMaterialy] = useState<Material[]>([]);

  // Edit states
  const [editingId, setEditingId] = useState<number | null>(null);
  const [showAddForm, setShowAddForm] = useState(false);

  useEffect(() => {
    fetchAllData();
  }, []);

  const fetchAllData = async () => {
    try {
      setLoading(true);
      const [produktyRes, maszynyRes, pracownicyRes, surowceRes, materialyRes] =
        await Promise.all([
          fetch("/api/produkty-direct"),
          fetch("/api/maszyny"),
          fetch("/api/pracownicy"),
          fetch("/api/surowce"),
          fetch("/api/materialy"),
        ]);

      const produktyData = await produktyRes.json();
      setProdukty(produktyData.products || produktyData);
      setMaszyny(await maszynyRes.json());
      setPracownicy(await pracownicyRes.json());
      setSurowce(await surowceRes.json());
      setMaterialy(await materialyRes.json());
    } catch (error) {
      console.error("Error fetching data:", error);
      alert("Błąd podczas ładowania danych");
    } finally {
      setLoading(false);
    }
  };

  const handleAdd = async (data: any) => {
    try {
      setSaving(true);
      const response = await fetch(`/api/admin/${activeTab}`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(data),
      });

      if (response.ok) {
        await fetchAllData();
        setShowAddForm(false);
        alert("Rekord został dodany pomyślnie!");
      } else {
        throw new Error("Failed to add record");
      }
    } catch (error) {
      console.error("Error adding record:", error);
      alert("Błąd podczas dodawania rekordu");
    } finally {
      setSaving(false);
    }
  };

  const handleSave = async (id: number, data: any) => {
    try {
      setSaving(true);
      const response = await fetch(`/api/admin/${activeTab}/${id}`, {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(data),
      });

      if (response.ok) {
        await fetchAllData();
        setEditingId(null);
        alert("Rekord został zaktualizowany pomyślnie!");
      } else {
        throw new Error("Failed to update record");
      }
    } catch (error) {
      console.error("Error updating record:", error);
      alert("Błąd podczas aktualizacji rekordu");
    } finally {
      setSaving(false);
    }
  };

  const handleDelete = async (id: number) => {
    if (!confirm("Czy na pewno chcesz usunąć ten rekord?")) return;

    try {
      setSaving(true);
      const response = await fetch(`/api/admin/${activeTab}/${id}`, {
        method: "DELETE",
      });

      if (response.ok) {
        await fetchAllData();
        alert("Rekord został usunięty pomyślnie!");
      } else {
        throw new Error("Failed to delete record");
      }
    } catch (error) {
      console.error("Error deleting record:", error);
      alert("Błąd podczas usuwania rekordu");
    } finally {
      setSaving(false);
    }
  };

  const tabs = [
    {
      id: "produkty" as TabType,
      label: "Produkty",
      icon: "📦",
      count: produkty.length,
    },
    {
      id: "maszyny" as TabType,
      label: "Maszyny",
      icon: "⚙️",
      count: maszyny.length,
    },
    {
      id: "pracownicy" as TabType,
      label: "Pracownicy",
      icon: "👥",
      count: pracownicy.length,
    },
    {
      id: "surowce" as TabType,
      label: "Surowce",
      icon: "🧱",
      count: surowce.length,
    },
    {
      id: "materialy" as TabType,
      label: "Materiały",
      icon: "🔧",
      count: materialy.length,
    },
  ];

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="bg-gray-800 rounded-xl p-8 border border-gray-700 shadow-xl">
          <div className="flex items-center space-x-4">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500"></div>
            <div className="text-xl text-white">Ładowanie danych...</div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-900 p-4">
      <div className="max-w-[1800px] mx-auto">
        {/* Header */}
        <div className="bg-gray-800 rounded-xl border border-gray-700 p-4 mb-4 shadow-xl">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <h1 className="text-2xl font-bold text-white">
                🗄️ Zarządzanie bazą danych
              </h1>
              <Link
                href="/nowy-raport"
                className="bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-all duration-300 border border-gray-600"
              >
                ← Powrót do raportu
              </Link>
            </div>
            <div className="flex items-center space-x-2">
              <button
                onClick={fetchAllData}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-all duration-300"
              >
                🔄 Odśwież
              </button>
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="bg-gray-800 rounded-xl border border-gray-700 mb-4 shadow-xl">
          <div className="flex border-b border-gray-700">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => {
                  setActiveTab(tab.id);
                  setEditingId(null);
                  setShowAddForm(false);
                }}
                className={`flex-1 px-6 py-4 text-center transition-all duration-300 ${
                  activeTab === tab.id
                    ? "bg-purple-600 text-white border-b-2 border-purple-400"
                    : "text-gray-300 hover:text-white hover:bg-gray-700"
                }`}
              >
                <div className="flex items-center justify-center space-x-2">
                  <span className="text-lg">{tab.icon}</span>
                  <span className="font-semibold">{tab.label}</span>
                  <span className="bg-gray-600 text-xs px-2 py-1 rounded-full">
                    {tab.count}
                  </span>
                </div>
              </button>
            ))}
          </div>
        </div>

        {/* Content Area */}
        <div className="bg-gray-800 rounded-xl border border-gray-700 p-6 shadow-xl">
          {/* Add New Button */}
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-bold text-white">
              {tabs.find((t) => t.id === activeTab)?.label}
            </h2>
            <button
              onClick={() => setShowAddForm(!showAddForm)}
              className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-all duration-300"
            >
              {showAddForm ? "❌ Anuluj" : "➕ Dodaj nowy"}
            </button>
          </div>

          {/* Add Form */}
          {showAddForm && (
            <AddForm
              activeTab={activeTab}
              onSave={handleAdd}
              onCancel={() => setShowAddForm(false)}
              saving={saving}
            />
          )}

          {/* Table Content */}
          <div className="overflow-x-auto">
            {activeTab === "produkty" && (
              <ProduktyTable
                data={produkty}
                editingId={editingId}
                onEdit={setEditingId}
                onSave={handleSave}
                onDelete={handleDelete}
                saving={saving}
              />
            )}
            {activeTab === "maszyny" && (
              <MaszynyTable
                data={maszyny}
                editingId={editingId}
                onEdit={setEditingId}
                onSave={handleSave}
                onDelete={handleDelete}
                saving={saving}
              />
            )}
            {activeTab === "pracownicy" && (
              <PracownicyTable
                data={pracownicy}
                editingId={editingId}
                onEdit={setEditingId}
                onSave={handleSave}
                onDelete={handleDelete}
                saving={saving}
              />
            )}
            {activeTab === "surowce" && (
              <SurowceTable
                data={surowce}
                editingId={editingId}
                onEdit={setEditingId}
                onSave={handleSave}
                onDelete={handleDelete}
                saving={saving}
              />
            )}
            {activeTab === "materialy" && (
              <MaterialyTable
                data={materialy}
                editingId={editingId}
                onEdit={setEditingId}
                onSave={handleSave}
                onDelete={handleDelete}
                saving={saving}
              />
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

// Add Form Component
function AddForm({
  activeTab,
  onSave,
  onCancel,
  saving,
}: {
  activeTab: TabType;
  onSave: (data: any) => void;
  onCancel: () => void;
  saving: boolean;
}) {
  const [formData, setFormData] = useState<any>({});

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(formData);
  };

  const handleChange = (field: string, value: string | number) => {
    setFormData({ ...formData, [field]: value });
  };

  return (
    <div className="mb-6 p-4 bg-gray-700 rounded-lg border border-gray-600">
      <h3 className="text-lg font-semibold text-white mb-4">
        Dodaj nowy {activeTab.slice(0, -1)}
      </h3>
      <form onSubmit={handleSubmit} className="space-y-4">
        {activeTab === "produkty" && (
          <ProduktyForm data={formData} onChange={handleChange} />
        )}
        {activeTab === "maszyny" && (
          <MaszynyForm data={formData} onChange={handleChange} />
        )}
        {activeTab === "pracownicy" && (
          <PracownicyForm data={formData} onChange={handleChange} />
        )}
        {activeTab === "surowce" && (
          <SurowceForm data={formData} onChange={handleChange} />
        )}
        {activeTab === "materialy" && (
          <MaterialyForm data={formData} onChange={handleChange} />
        )}

        <div className="flex space-x-2">
          <button
            type="submit"
            disabled={saving}
            className="bg-green-600 hover:bg-green-700 disabled:bg-gray-600 text-white px-4 py-2 rounded-lg transition-all duration-300"
          >
            {saving ? "Zapisywanie..." : "💾 Zapisz"}
          </button>
          <button
            type="button"
            onClick={onCancel}
            className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg transition-all duration-300"
          >
            ❌ Anuluj
          </button>
        </div>
      </form>
    </div>
  );
}
