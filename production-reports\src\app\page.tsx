import Link from "next/link";

export default function Home() {
  return (
    <div className="min-h-screen bg-gray-900 py-8">
      <div className="max-w-6xl mx-auto px-4">
        <div className="bg-gray-800 rounded-xl shadow-2xl border border-gray-700 p-8">
          <div className="text-center mb-8">
            <h1 className="text-4xl font-bold text-white mb-4">
              System Raportów Produkcyjnych
            </h1>
            <p className="text-gray-300 text-lg">
              Nowoczesne zarządzanie raportami produkcyjnymi
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Link
              href="/nowy-raport"
              className="group bg-blue-600 hover:bg-blue-700 text-white p-8 rounded-xl text-center transition-all duration-300 transform hover:scale-105 hover:shadow-xl border border-gray-700"
            >
              <div className="text-3xl mb-4">📝</div>
              <div className="text-2xl font-semibold mb-2">Nowy Raport</div>
              <div className="text-blue-100 group-hover:text-white transition-colors">
                Dodaj nowy raport produkcyjny
              </div>
            </Link>

            <Link
              href="/raporty"
              className="group bg-green-600 hover:bg-green-700 text-white p-8 rounded-xl text-center transition-all duration-300 transform hover:scale-105 hover:shadow-xl border border-gray-700"
            >
              <div className="text-3xl mb-4">📊</div>
              <div className="text-2xl font-semibold mb-2">
                Przeglądaj Raporty
              </div>
              <div className="text-green-100 group-hover:text-white transition-colors">
                Zobacz wszystkie raporty
              </div>
            </Link>

            <Link
              href="/produkty"
              className="group bg-purple-600 hover:bg-purple-700 text-white p-8 rounded-xl text-center transition-all duration-300 transform hover:scale-105 hover:shadow-xl border border-gray-700"
            >
              <div className="text-3xl mb-4">📦</div>
              <div className="text-2xl font-semibold mb-2">Produkty</div>
              <div className="text-purple-100 group-hover:text-white transition-colors">
                Zarządzaj produktami
              </div>
            </Link>

            <Link
              href="/ustawienia"
              className="group bg-gray-600 hover:bg-gray-700 text-white p-8 rounded-xl text-center transition-all duration-300 transform hover:scale-105 hover:shadow-xl border border-gray-700"
            >
              <div className="text-3xl mb-4">⚙️</div>
              <div className="text-2xl font-semibold mb-2">Ustawienia</div>
              <div className="text-gray-100 group-hover:text-white transition-colors">
                Maszyny, pracownicy, materiały
              </div>
            </Link>
          </div>

          <div className="mt-8 text-center">
            <p className="text-gray-400 text-sm">
              Wersja 2.0 - Nowoczesny interfejs z ciemnym motywem
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
