import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id);
    const data = await request.json();

    const maszyna = await prisma.maszyna.update({
      where: { id },
      data: {
        nazwa: data.nazwa,
        typ: data.typ || null,
        info: data.info || null,
      },
    });

    return NextResponse.json(maszyna);
  } catch (error) {
    console.error("Błąd podczas aktualizacji maszyny:", error);
    return NextResponse.json(
      { error: "Błąd podczas aktualizacji maszyny" },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id);

    await prisma.maszyna.delete({
      where: { id },
    });

    return NextResponse.json({ message: "<PERSON><PERSON><PERSON> została usunięta" });
  } catch (error) {
    console.error("Błąd podczas usuwania maszyny:", error);
    return NextResponse.json(
      { error: "Błąd podczas usuwania maszyny" },
      { status: 500 }
    );
  }
}
