import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";

export async function GET() {
  try {
    const maszyny = await prisma.maszyna.findMany({
      orderBy: {
        nazwa: "asc",
      },
    });

    return NextResponse.json(maszyny);
  } catch (error) {
    console.error("Błąd podczas pobierania maszyn:", error);
    return NextResponse.json(
      { error: "Błąd podczas pobierania maszyn" },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const data = await request.json();

    const maszyna = await prisma.maszyna.create({
      data: {
        nazwa: data.nazwa,
        typ: data.typ || null,
        info: data.info || null,
      },
    });

    return NextResponse.json(maszyna);
  } catch (error) {
    console.error("Błąd podczas dodawania maszyny:", error);
    return NextResponse.json(
      { error: "Błąd podczas dodawania maszyny" },
      { status: 500 }
    );
  }
}
