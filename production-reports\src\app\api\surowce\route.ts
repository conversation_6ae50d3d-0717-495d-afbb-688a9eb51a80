import { NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";

export async function GET() {
  try {
    const surowce = await prisma.surowiec.findMany({
      orderBy: {
        nazwa: "asc",
      },
    });

    return NextResponse.json(surowce);
  } catch (error) {
    console.error("Błąd podczas pobierania surowców:", error);
    return NextResponse.json(
      { error: "Błąd podczas pobierania surowców" },
      { status: 500 }
    );
  }
}
