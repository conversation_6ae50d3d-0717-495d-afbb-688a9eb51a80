import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";

export async function GET() {
  try {
    const surowce = await prisma.surowiec.findMany({
      orderBy: {
        nazwa: "asc",
      },
    });

    return NextResponse.json(surowce);
  } catch (error) {
    console.error("Błąd podczas pobierania surowców:", error);
    return NextResponse.json(
      { error: "Błąd podczas pobierania surowców" },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const data = await request.json();

    const surowiec = await prisma.surowiec.create({
      data: {
        nazwa: data.nazwa,
        typ: data.typ || null,
        info: data.info || null,
      },
    });

    return NextResponse.json(surowiec);
  } catch (error) {
    console.error("Błąd podczas dodawania surowca:", error);
    return NextResponse.json(
      { error: "Błąd podczas dodawania surowca" },
      { status: 500 }
    );
  }
}
