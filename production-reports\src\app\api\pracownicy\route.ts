import { NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";

export async function GET() {
  try {
    const pracownicy = await prisma.pracownik.findMany({
      orderBy: {
        numer: "asc",
      },
    });

    return NextResponse.json(pracownicy);
  } catch (error) {
    console.error("Błąd podczas pobierania pracowników:", error);
    return NextResponse.json(
      { error: "Błąd podczas pobierania pracowników" },
      { status: 500 }
    );
  }
}
