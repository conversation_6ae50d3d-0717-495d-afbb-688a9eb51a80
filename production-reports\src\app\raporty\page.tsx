"use client";

import { useState, useEffect } from "react";
import Link from "next/link";

interface RaportProdukcyjny {
  id: number;
  data_utworzenia: string;
  produkt_id: number;
  metraz_rolek: number;
  ilosc_rolek: number;
  nr_zamowienia: string | null;
  waga: number | null;
  zuzyty_surowiec: number;
  odpad_surowiec: number;
  szarza_surowca: string | null;
  zuzyty_material: number;
  odpad_material: number;
  czas_pracy_maszyny: number;
  czas_pracy_pracownika: number;
  uwagi: string | null;
  surowiec_id: number;
  material_id: number;
  maszyna_id: number;
  pracownik_id: number;
  // Related data from joins
  produkt_kod: string | null;
  produkt_nazwa: string | null;
  maszyna_nazwa: string | null;
  pracownik_imie: string | null;
  pracownik_nazwisko: string | null;
  surowiec_nazwa: string | null;
  material_nazwa: string | null;
}

interface Produkt {
  id: number;
  kod_handlowy: string | null;
  nazwa: string | null;
  metraz: number;
  info: string | null;
}

interface Maszyna {
  id: number;
  nazwa: string;
  typ: string | null;
  info: string | null;
}

interface Pracownik {
  id: number;
  numer: string;
  imie: string | null;
  nazwisko: string | null;
  stanowisko: string | null;
}

interface Surowiec {
  id: number;
  nazwa: string;
  typ: string | null;
  info: string | null;
}

interface Material {
  id: number;
  nazwa: string;
  typ: string | null;
  info: string | null;
}

export default function RaportyPage() {
  const [raporty, setRaporty] = useState<RaportProdukcyjny[]>([]);
  const [loading, setLoading] = useState(true);

  const [produkty, setProdukty] = useState<Produkt[]>([]);
  const [maszyny, setMaszyny] = useState<Maszyna[]>([]);
  const [pracownicy, setPracownicy] = useState<Pracownik[]>([]);
  const [surowce, setSurowce] = useState<Surowiec[]>([]);
  const [materialy, setMaterialy] = useState<Material[]>([]);

  useEffect(() => {
    const loadData = async () => {
      try {
        await Promise.all([fetchRaporty(), fetchMasterData()]);
      } catch (error) {
        console.error("Błąd podczas ładowania danych:", error);
      } finally {
        setLoading(false);
      }
    };
    loadData();
  }, []);

  const fetchRaporty = async () => {
    try {
      const response = await fetch(`/api/raporty-direct`);
      const data = await response.json();
      setRaporty(data);
    } catch (error) {
      console.error("Błąd podczas ładowania raportów:", error);
    }
  };

  const fetchMasterData = async () => {
    try {
      const [produktyRes, maszynyRes, pracownicyRes, surowceRes, materialyRes] =
        await Promise.all([
          fetch("/api/produkty-direct"),
          fetch("/api/maszyny"),
          fetch("/api/pracownicy"),
          fetch("/api/surowce"),
          fetch("/api/materialy"),
        ]);

      setProdukty(await produktyRes.json());
      setMaszyny(await maszynyRes.json());
      setPracownicy(await pracownicyRes.json());
      setSurowce(await surowceRes.json());
      setMaterialy(await materialyRes.json());
    } catch (error) {
      console.error("Błąd podczas ładowania danych:", error);
    }
  };

  const handleEdit = (raport: RaportProdukcyjny) => {
    // Redirect to edit page instead of inline editing
    window.location.href = `/edytuj-raport/${raport.id}`;
  };

  const handleDelete = async (id: number) => {
    if (!confirm("Czy na pewno chcesz usunąć ten raport?")) return;

    try {
      const response = await fetch(`/api/raporty-direct/${id}`, {
        method: "DELETE",
      });

      if (response.ok) {
        await fetchRaporty();
      } else {
        console.error("Błąd podczas usuwania raportu");
      }
    } catch (error) {
      console.error("Błąd podczas usuwania raportu:", error);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-white text-xl">
          Ładowanie... (Reports: {raporty.length}, Products: {produkty.length})
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 p-4 relative overflow-hidden">
      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-blue-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse animation-delay-2000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-indigo-500 rounded-full mix-blend-multiply filter blur-xl opacity-10 animate-pulse animation-delay-4000"></div>
      </div>

      <div className="max-w-[1600px] mx-auto relative z-10">
        {/* Header */}
        <div className="bg-gradient-to-r from-gray-800 via-gray-700 to-gray-800 rounded-2xl border border-gray-600 p-6 mb-6 shadow-2xl backdrop-blur-sm bg-opacity-90">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-6">
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
                  <span className="text-white font-bold text-lg">📊</span>
                </div>
                <div>
                  <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
                    RAPORTY PRODUKCYJNE
                  </h1>
                  <p className="text-gray-400 text-sm">
                    Lista wszystkich raportów produkcyjnych
                  </p>
                </div>
              </div>
              <div className="flex space-x-3">
                <Link
                  href="/"
                  className="group bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-500 hover:to-gray-600 text-white px-6 py-3 rounded-xl transition-all duration-300 border border-gray-500 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
                >
                  <span className="flex items-center space-x-2">
                    <span className="transform group-hover:-translate-x-1 transition-transform duration-300">
                      ←
                    </span>
                    <span>Menu Główne</span>
                  </span>
                </Link>
                <Link
                  href="/nowy-raport"
                  className="group bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-500 hover:to-purple-500 text-white px-6 py-3 rounded-xl transition-all duration-300 border border-blue-400 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
                >
                  <span className="flex items-center space-x-2">
                    <span className="transform group-hover:rotate-12 transition-transform duration-300">
                      ➕
                    </span>
                    <span>Nowy Raport</span>
                  </span>
                </Link>
                <button
                  onClick={fetchRaporty}
                  className="group bg-gradient-to-r from-green-600 to-teal-600 hover:from-green-500 hover:to-teal-500 text-white px-6 py-3 rounded-xl transition-all duration-300 border border-green-400 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
                >
                  <span className="flex items-center space-x-2">
                    <span className="transform group-hover:rotate-180 transition-transform duration-300">
                      🔄
                    </span>
                    <span>Odśwież</span>
                  </span>
                </button>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <div className="flex items-center space-x-2 bg-green-900 bg-opacity-50 px-4 py-2 rounded-full border border-green-500">
                <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse shadow-lg shadow-green-400/50"></div>
                <span className="text-green-300 font-semibold text-sm">
                  System Online
                </span>
              </div>
              <div className="text-gray-400 text-sm">
                {new Date().toLocaleTimeString("pl-PL")}
              </div>
            </div>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="bg-gradient-to-br from-gray-800 via-gray-700 to-gray-800 rounded-2xl border border-gray-600 p-6 mb-6 shadow-2xl backdrop-blur-sm bg-opacity-90">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="bg-gradient-to-br from-blue-600/20 to-blue-800/20 border border-blue-500/30 rounded-xl p-6 shadow-lg backdrop-blur-sm">
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-blue-400 text-sm font-medium mb-1">
                    Łączna liczba raportów
                  </div>
                  <div className="text-3xl font-bold bg-gradient-to-r from-blue-400 to-blue-300 bg-clip-text text-transparent">
                    {raporty.length}
                  </div>
                </div>
                <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center shadow-lg">
                  <span className="text-white text-xl">📊</span>
                </div>
              </div>
            </div>

            <div className="bg-gradient-to-br from-green-600/20 to-green-800/20 border border-green-500/30 rounded-xl p-6 shadow-lg backdrop-blur-sm">
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-green-400 text-sm font-medium mb-1">
                    Łączne metry
                  </div>
                  <div className="text-3xl font-bold bg-gradient-to-r from-green-400 to-green-300 bg-clip-text text-transparent">
                    {raporty
                      .reduce(
                        (sum, r) => sum + r.metraz_rolek * r.ilosc_rolek,
                        0
                      )
                      .toFixed(0)}
                    <span className="text-lg ml-1">m</span>
                  </div>
                </div>
                <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center shadow-lg">
                  <span className="text-white text-xl">📏</span>
                </div>
              </div>
            </div>

            <div className="bg-gradient-to-br from-purple-600/20 to-purple-800/20 border border-purple-500/30 rounded-xl p-6 shadow-lg backdrop-blur-sm">
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-purple-400 text-sm font-medium mb-1">
                    Łączna ilość sztuk
                  </div>
                  <div className="text-3xl font-bold bg-gradient-to-r from-purple-400 to-purple-300 bg-clip-text text-transparent">
                    {raporty.reduce((sum, r) => sum + r.ilosc_rolek, 0)}
                  </div>
                </div>
                <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
                  <span className="text-white text-xl">📦</span>
                </div>
              </div>
            </div>

            <div className="bg-gradient-to-br from-orange-600/20 to-orange-800/20 border border-orange-500/30 rounded-xl p-6 shadow-lg backdrop-blur-sm">
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-orange-400 text-sm font-medium mb-1">
                    Łączny czas pracy (h)
                  </div>
                  <div className="text-3xl font-bold bg-gradient-to-r from-orange-400 to-orange-300 bg-clip-text text-transparent">
                    {raporty
                      .reduce((sum, r) => sum + r.czas_pracy_maszyny, 0)
                      .toFixed(1)}
                  </div>
                </div>
                <div className="w-12 h-12 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl flex items-center justify-center shadow-lg">
                  <span className="text-white text-xl">⏱️</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content Area with Table */}
        <div className="bg-gradient-to-br from-gray-800 via-gray-700 to-gray-800 rounded-2xl border border-gray-600 shadow-2xl backdrop-blur-sm bg-opacity-90">
          <div className="overflow-x-auto">
            {/* Debug: raporty.length = {raporty.length} */}
            {raporty.length === 0 ? (
              <div className="text-center py-16 p-6">
                <div className="relative mb-8">
                  <div className="text-8xl mb-4">📊</div>
                  <div className="absolute inset-0 bg-blue-500/20 rounded-full blur-3xl"></div>
                </div>
                <h3 className="text-2xl font-bold bg-gradient-to-r from-gray-300 to-gray-100 bg-clip-text text-transparent mb-4">
                  Brak raportów w bazie danych
                </h3>
                <p className="text-gray-400 mb-8">
                  Rozpocznij pracę tworząc pierwszy raport produkcyjny
                </p>
                <Link
                  href="/nowy-raport"
                  className="group bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-500 hover:to-purple-500 text-white px-8 py-4 rounded-xl transition-all duration-300 border border-blue-400 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 inline-flex items-center space-x-2 font-semibold"
                >
                  <span className="transform group-hover:rotate-12 transition-transform duration-300">
                    ➕
                  </span>
                  <span>Dodaj pierwszy raport</span>
                </Link>
              </div>
            ) : (
              <div className="p-6">
                <div className="flex items-center mb-6">
                  <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg mr-3">
                    <span className="text-white font-bold">📋</span>
                  </div>
                  <h3 className="text-xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
                    Lista raportów produkcyjnych
                  </h3>
                </div>

                <div className="overflow-x-auto rounded-xl border border-gray-600">
                  <table className="w-full text-sm">
                    <thead>
                      <tr className="bg-gradient-to-r from-gray-700 to-gray-600 border-b border-gray-500">
                        <th className="px-3 py-4 text-left text-gray-200 font-semibold border-r border-gray-500 w-12 bg-gradient-to-r from-blue-600/20 to-blue-500/20">
                          <span className="flex items-center space-x-1">
                            <span className="w-2 h-2 bg-blue-400 rounded-full"></span>
                            <span>ID</span>
                          </span>
                        </th>
                        <th className="px-3 py-4 text-left text-gray-200 font-semibold border-r border-gray-500 min-w-[120px] bg-gradient-to-r from-green-600/20 to-green-500/20">
                          <span className="flex items-center space-x-1">
                            <span className="w-2 h-2 bg-green-400 rounded-full"></span>
                            <span>Kod handlowy</span>
                          </span>
                        </th>
                        <th className="px-3 py-4 text-left text-gray-200 font-semibold border-r border-gray-500 min-w-[200px] bg-gradient-to-r from-purple-600/20 to-purple-500/20">
                          <span className="flex items-center space-x-1">
                            <span className="w-2 h-2 bg-purple-400 rounded-full"></span>
                            <span>Nazwa</span>
                          </span>
                        </th>
                        <th className="px-2 py-4 text-center text-gray-200 font-semibold border-r border-gray-500 w-24">
                          Nr zamówienia
                        </th>
                        <th className="px-2 py-4 text-center text-gray-200 font-semibold border-r border-gray-500 w-20">
                          Waga
                        </th>
                        <th className="px-2 py-4 text-center text-gray-200 font-semibold border-r border-gray-500 w-20">
                          Metraż rolki
                        </th>
                        <th className="px-2 py-4 text-center text-gray-200 font-semibold border-r border-gray-500 w-16">
                          Ilość szt
                        </th>
                        <th className="px-2 py-4 text-center text-gray-200 font-semibold border-r border-gray-500 w-20">
                          Suma metrów
                        </th>
                        <th className="px-2 py-4 text-center text-gray-200 font-semibold border-r border-gray-500 w-24">
                          Surowiec użyty
                        </th>
                        <th className="px-2 py-4 text-center text-gray-200 font-semibold border-r border-gray-500 w-24">
                          Szarża surowca
                        </th>
                        <th className="px-2 py-4 text-center text-gray-200 font-semibold border-r border-gray-500 w-24">
                          Zużycie surowca
                        </th>
                        <th className="px-2 py-4 text-center text-gray-200 font-semibold border-r border-gray-500 w-24">
                          Odpad nieużytkowy
                        </th>
                        <th className="px-2 py-4 text-center text-gray-200 font-semibold border-r border-gray-500 w-20">
                          Nr maszyny
                        </th>
                        <th className="px-2 py-4 text-center text-gray-200 font-semibold border-r border-gray-500 w-20">
                          ID pracownika
                        </th>
                        <th className="px-2 py-4 text-center text-gray-200 font-semibold border-r border-gray-500 w-20">
                          Czas pracy
                        </th>
                        <th className="px-2 py-4 text-center text-gray-200 font-semibold w-24">
                          Akcje
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-gray-800/50">
                      {raporty.map((raport, index) => (
                        <tr
                          key={raport.id}
                          className={`border-b border-gray-600/50 transition-all duration-300 cursor-pointer hover:bg-gradient-to-r hover:from-blue-900/30 hover:to-purple-900/30 group ${
                            index % 2 === 0
                              ? "bg-gray-800/30"
                              : "bg-gray-700/30"
                          }`}
                          onClick={() => handleEdit(raport)}
                        >
                          <td className="px-2 py-2 text-center text-white border-r border-gray-600 font-medium">
                            {raport.id}
                          </td>
                          <td className="px-3 py-2 text-white border-r border-gray-600 font-medium">
                            {raport.produkt_kod || "-"}
                          </td>
                          <td className="px-3 py-2 text-white border-r border-gray-600">
                            {raport.produkt_nazwa || "-"}
                          </td>
                          <td className="px-2 py-2 text-center text-white border-r border-gray-600">
                            {raport.nr_zamowienia || "-"}
                          </td>
                          <td className="px-2 py-2 text-center text-white border-r border-gray-600 font-mono">
                            {raport.waga?.toFixed(1) || "-"}
                          </td>
                          <td className="px-2 py-2 text-center text-white border-r border-gray-600 font-mono">
                            {raport.metraz_rolek.toFixed(0)}
                          </td>
                          <td className="px-2 py-2 text-center text-white border-r border-gray-600 font-mono">
                            {raport.ilosc_rolek}
                          </td>
                          <td className="px-2 py-2 text-center text-white border-r border-gray-600 font-mono">
                            {(raport.metraz_rolek * raport.ilosc_rolek).toFixed(
                              0
                            )}
                          </td>
                          <td className="px-2 py-2 text-center text-white border-r border-gray-600">
                            {raport.surowiec_nazwa || "-"}
                          </td>
                          <td className="px-2 py-2 text-center text-white border-r border-gray-600">
                            {raport.szarza_surowca || "-"}
                          </td>
                          <td className="px-2 py-2 text-center text-white border-r border-gray-600 font-mono">
                            {raport.zuzyty_surowiec?.toFixed(1) || "0.0"}
                          </td>
                          <td className="px-2 py-2 text-center text-white border-r border-gray-600 font-mono">
                            {raport.odpad_surowiec?.toFixed(1) || "0.0"}
                          </td>
                          <td className="px-2 py-2 text-center text-white border-r border-gray-600">
                            <span className="bg-blue-600 text-white px-1 py-0.5 rounded text-xs">
                              {raport.maszyna_nazwa || "-"}
                            </span>
                          </td>
                          <td className="px-2 py-2 text-center text-white border-r border-gray-600">
                            <span className="bg-green-600 text-white px-1 py-0.5 rounded text-xs">
                              {pracownicy.find(
                                (p) => p.id === raport.pracownik_id
                              )?.numer || "-"}
                            </span>
                          </td>
                          <td className="px-2 py-2 text-center text-white border-r border-gray-600 font-mono">
                            {raport.czas_pracy_maszyny?.toFixed(1) || "0.0"}
                          </td>
                          <td className="px-2 py-3 text-center">
                            <div className="flex justify-center space-x-2">
                              <button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleEdit(raport);
                                }}
                                className="group bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-500 hover:to-blue-600 text-white px-3 py-1.5 rounded-lg text-xs transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 border border-blue-400"
                              >
                                <span className="transform group-hover:scale-110 transition-transform duration-300">
                                  ✏️
                                </span>
                              </button>
                              <button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleDelete(raport.id);
                                }}
                                className="group bg-gradient-to-r from-red-600 to-red-700 hover:from-red-500 hover:to-red-600 text-white px-3 py-1.5 rounded-lg text-xs transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 border border-red-400"
                              >
                                <span className="transform group-hover:scale-110 transition-transform duration-300">
                                  🗑️
                                </span>
                              </button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
