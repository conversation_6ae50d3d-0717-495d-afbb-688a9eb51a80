"use client";

import { useState } from "react";

// Form Components
export function ProduktyForm({
  data,
  onChange,
}: {
  data: any;
  onChange: (field: string, value: string | number) => void;
}) {
  return (
    <div className="grid grid-cols-3 gap-4">
      <div>
        <label className="block text-sm text-gray-300 mb-1">Kod handlowy</label>
        <input
          type="text"
          value={data.kod_handlowy || ""}
          onChange={(e) => onChange("kod_handlowy", e.target.value)}
          className="w-full p-2 bg-gray-600 border border-gray-500 rounded text-white text-sm focus:ring-2 focus:ring-purple-500"
          placeholder="Kod handlowy"
        />
      </div>
      <div>
        <label className="block text-sm text-gray-300 mb-1">Nazwa</label>
        <input
          type="text"
          value={data.nazwa || ""}
          onChange={(e) => onChange("nazwa", e.target.value)}
          className="w-full p-2 bg-gray-600 border border-gray-500 rounded text-white text-sm focus:ring-2 focus:ring-purple-500"
          placeholder="Nazwa produktu"
        />
      </div>
      <div>
        <label className="block text-sm text-gray-300 mb-1">Metraż</label>
        <input
          type="number"
          step="0.01"
          value={data.metraz || ""}
          onChange={(e) => onChange("metraz", parseFloat(e.target.value) || 0)}
          className="w-full p-2 bg-gray-600 border border-gray-500 rounded text-white text-sm focus:ring-2 focus:ring-purple-500"
          placeholder="0.00"
        />
      </div>
      <div>
        <label className="block text-sm text-gray-300 mb-1">
          Wydajność [m/8h]
        </label>
        <input
          type="number"
          step="0.01"
          value={data.wydajnosc || ""}
          onChange={(e) =>
            onChange("wydajnosc", parseFloat(e.target.value) || 0)
          }
          className="w-full p-2 bg-gray-600 border border-gray-500 rounded text-white text-sm focus:ring-2 focus:ring-purple-500"
          placeholder="0.00"
        />
      </div>
      <div>
        <label className="block text-sm text-gray-300 mb-1">Surowiec</label>
        <input
          type="text"
          value={data.surowiec || ""}
          onChange={(e) => onChange("surowiec", e.target.value)}
          className="w-full p-2 bg-gray-600 border border-gray-500 rounded text-white text-sm focus:ring-2 focus:ring-purple-500"
          placeholder="Nazwa surowca"
        />
      </div>
      <div>
        <label className="block text-sm text-gray-300 mb-1">Materiał</label>
        <input
          type="text"
          value={data.material || ""}
          onChange={(e) => onChange("material", e.target.value)}
          className="w-full p-2 bg-gray-600 border border-gray-500 rounded text-white text-sm focus:ring-2 focus:ring-purple-500"
          placeholder="Nazwa materiału"
        />
      </div>
      <div>
        <label className="block text-sm text-gray-300 mb-1">
          Procent materiału
        </label>
        <input
          type="number"
          step="0.1"
          value={data.material_percent || ""}
          onChange={(e) =>
            onChange("material_percent", parseFloat(e.target.value) || 0)
          }
          className="w-full p-2 bg-gray-600 border border-gray-500 rounded text-white text-sm focus:ring-2 focus:ring-purple-500"
          placeholder="0.0"
        />
      </div>

      {/* Material 1 */}
      <div>
        <label className="block text-sm text-gray-300 mb-1">Materiał 1</label>
        <input
          type="text"
          value={data.material1 || ""}
          onChange={(e) => onChange("material1", e.target.value)}
          className="w-full p-2 bg-gray-600 border border-gray-500 rounded text-white text-sm focus:ring-2 focus:ring-purple-500"
          placeholder="Nazwa materiału 1"
        />
      </div>
      <div>
        <label className="block text-sm text-gray-300 mb-1">
          Procent materiału 1
        </label>
        <input
          type="number"
          step="0.1"
          value={data.material1_percent || ""}
          onChange={(e) =>
            onChange("material1_percent", parseFloat(e.target.value) || 0)
          }
          className="w-full p-2 bg-gray-600 border border-gray-500 rounded text-white text-sm focus:ring-2 focus:ring-purple-500"
          placeholder="0.0"
        />
      </div>

      {/* Material 2 */}
      <div>
        <label className="block text-sm text-gray-300 mb-1">Materiał 2</label>
        <input
          type="text"
          value={data.material2 || ""}
          onChange={(e) => onChange("material2", e.target.value)}
          className="w-full p-2 bg-gray-600 border border-gray-500 rounded text-white text-sm focus:ring-2 focus:ring-purple-500"
          placeholder="Nazwa materiału 2"
        />
      </div>
      <div>
        <label className="block text-sm text-gray-300 mb-1">
          Procent materiału 2
        </label>
        <input
          type="number"
          step="0.1"
          value={data.material2_percent || ""}
          onChange={(e) =>
            onChange("material2_percent", parseFloat(e.target.value) || 0)
          }
          className="w-full p-2 bg-gray-600 border border-gray-500 rounded text-white text-sm focus:ring-2 focus:ring-purple-500"
          placeholder="0.0"
        />
      </div>

      {/* Material 3 */}
      <div>
        <label className="block text-sm text-gray-300 mb-1">Materiał 3</label>
        <input
          type="text"
          value={data.material3 || ""}
          onChange={(e) => onChange("material3", e.target.value)}
          className="w-full p-2 bg-gray-600 border border-gray-500 rounded text-white text-sm focus:ring-2 focus:ring-purple-500"
          placeholder="Nazwa materiału 3"
        />
      </div>
      <div>
        <label className="block text-sm text-gray-300 mb-1">
          Procent materiału 3
        </label>
        <input
          type="number"
          step="0.1"
          value={data.material3_percent || ""}
          onChange={(e) =>
            onChange("material3_percent", parseFloat(e.target.value) || 0)
          }
          className="w-full p-2 bg-gray-600 border border-gray-500 rounded text-white text-sm focus:ring-2 focus:ring-purple-500"
          placeholder="0.0"
        />
      </div>

      {/* Material 4 */}
      <div>
        <label className="block text-sm text-gray-300 mb-1">Materiał 4</label>
        <input
          type="text"
          value={data.material4 || ""}
          onChange={(e) => onChange("material4", e.target.value)}
          className="w-full p-2 bg-gray-600 border border-gray-500 rounded text-white text-sm focus:ring-2 focus:ring-purple-500"
          placeholder="Nazwa materiału 4"
        />
      </div>
      <div>
        <label className="block text-sm text-gray-300 mb-1">
          Procent materiału 4
        </label>
        <input
          type="number"
          step="0.1"
          value={data.material4_percent || ""}
          onChange={(e) =>
            onChange("material4_percent", parseFloat(e.target.value) || 0)
          }
          className="w-full p-2 bg-gray-600 border border-gray-500 rounded text-white text-sm focus:ring-2 focus:ring-purple-500"
          placeholder="0.0"
        />
      </div>

      {/* Material 5 */}
      <div>
        <label className="block text-sm text-gray-300 mb-1">Materiał 5</label>
        <input
          type="text"
          value={data.material5 || ""}
          onChange={(e) => onChange("material5", e.target.value)}
          className="w-full p-2 bg-gray-600 border border-gray-500 rounded text-white text-sm focus:ring-2 focus:ring-purple-500"
          placeholder="Nazwa materiału 5"
        />
      </div>
      <div>
        <label className="block text-sm text-gray-300 mb-1">
          Procent materiału 5
        </label>
        <input
          type="number"
          step="0.1"
          value={data.material5_percent || ""}
          onChange={(e) =>
            onChange("material5_percent", parseFloat(e.target.value) || 0)
          }
          className="w-full p-2 bg-gray-600 border border-gray-500 rounded text-white text-sm focus:ring-2 focus:ring-purple-500"
          placeholder="0.0"
        />
      </div>

      <div>
        <label className="block text-sm text-gray-300 mb-1">Info</label>
        <input
          type="text"
          value={data.info || ""}
          onChange={(e) => onChange("info", e.target.value)}
          className="w-full p-2 bg-gray-600 border border-gray-500 rounded text-white text-sm focus:ring-2 focus:ring-purple-500"
          placeholder="Dodatkowe informacje"
        />
      </div>
    </div>
  );
}

export function MaszynyForm({
  data,
  onChange,
}: {
  data: any;
  onChange: (field: string, value: string | number) => void;
}) {
  return (
    <div className="grid grid-cols-2 gap-4">
      <div>
        <label className="block text-sm text-gray-300 mb-1">Nazwa *</label>
        <input
          type="text"
          value={data.nazwa || ""}
          onChange={(e) => onChange("nazwa", e.target.value)}
          required
          className="w-full p-2 bg-gray-600 border border-gray-500 rounded text-white text-sm focus:ring-2 focus:ring-purple-500"
          placeholder="Nazwa maszyny"
        />
      </div>
      <div>
        <label className="block text-sm text-gray-300 mb-1">Typ</label>
        <input
          type="text"
          value={data.typ || ""}
          onChange={(e) => onChange("typ", e.target.value)}
          className="w-full p-2 bg-gray-600 border border-gray-500 rounded text-white text-sm focus:ring-2 focus:ring-purple-500"
          placeholder="Typ maszyny"
        />
      </div>
      <div className="col-span-2">
        <label className="block text-sm text-gray-300 mb-1">Info</label>
        <textarea
          value={data.info || ""}
          onChange={(e) => onChange("info", e.target.value)}
          rows={3}
          className="w-full p-2 bg-gray-600 border border-gray-500 rounded text-white text-sm focus:ring-2 focus:ring-purple-500 resize-none"
          placeholder="Dodatkowe informacje"
        />
      </div>
    </div>
  );
}

export function PracownicyForm({
  data,
  onChange,
}: {
  data: any;
  onChange: (field: string, value: string | number) => void;
}) {
  return (
    <div className="grid grid-cols-2 gap-4">
      <div>
        <label className="block text-sm text-gray-300 mb-1">Numer *</label>
        <input
          type="text"
          value={data.numer || ""}
          onChange={(e) => onChange("numer", e.target.value)}
          required
          className="w-full p-2 bg-gray-600 border border-gray-500 rounded text-white text-sm focus:ring-2 focus:ring-purple-500"
          placeholder="Numer pracownika"
        />
      </div>
      <div>
        <label className="block text-sm text-gray-300 mb-1">Stanowisko</label>
        <input
          type="text"
          value={data.stanowisko || ""}
          onChange={(e) => onChange("stanowisko", e.target.value)}
          className="w-full p-2 bg-gray-600 border border-gray-500 rounded text-white text-sm focus:ring-2 focus:ring-purple-500"
          placeholder="Stanowisko"
        />
      </div>
      <div>
        <label className="block text-sm text-gray-300 mb-1">Imię</label>
        <input
          type="text"
          value={data.imie || ""}
          onChange={(e) => onChange("imie", e.target.value)}
          className="w-full p-2 bg-gray-600 border border-gray-500 rounded text-white text-sm focus:ring-2 focus:ring-purple-500"
          placeholder="Imię"
        />
      </div>
      <div>
        <label className="block text-sm text-gray-300 mb-1">Nazwisko</label>
        <input
          type="text"
          value={data.nazwisko || ""}
          onChange={(e) => onChange("nazwisko", e.target.value)}
          className="w-full p-2 bg-gray-600 border border-gray-500 rounded text-white text-sm focus:ring-2 focus:ring-purple-500"
          placeholder="Nazwisko"
        />
      </div>
    </div>
  );
}

export function SurowceForm({
  data,
  onChange,
}: {
  data: any;
  onChange: (field: string, value: string | number) => void;
}) {
  return (
    <div className="grid grid-cols-2 gap-4">
      <div>
        <label className="block text-sm text-gray-300 mb-1">Nazwa *</label>
        <input
          type="text"
          value={data.nazwa || ""}
          onChange={(e) => onChange("nazwa", e.target.value)}
          required
          className="w-full p-2 bg-gray-600 border border-gray-500 rounded text-white text-sm focus:ring-2 focus:ring-purple-500"
          placeholder="Nazwa surowca"
        />
      </div>
      <div>
        <label className="block text-sm text-gray-300 mb-1">Typ</label>
        <input
          type="text"
          value={data.typ || ""}
          onChange={(e) => onChange("typ", e.target.value)}
          className="w-full p-2 bg-gray-600 border border-gray-500 rounded text-white text-sm focus:ring-2 focus:ring-purple-500"
          placeholder="Typ surowca"
        />
      </div>
      <div className="col-span-2">
        <label className="block text-sm text-gray-300 mb-1">Info</label>
        <textarea
          value={data.info || ""}
          onChange={(e) => onChange("info", e.target.value)}
          rows={3}
          className="w-full p-2 bg-gray-600 border border-gray-500 rounded text-white text-sm focus:ring-2 focus:ring-purple-500 resize-none"
          placeholder="Dodatkowe informacje"
        />
      </div>
    </div>
  );
}

export function MaterialyForm({
  data,
  onChange,
}: {
  data: any;
  onChange: (field: string, value: string | number) => void;
}) {
  return (
    <div className="grid grid-cols-2 gap-4">
      <div>
        <label className="block text-sm text-gray-300 mb-1">Nazwa *</label>
        <input
          type="text"
          value={data.nazwa || ""}
          onChange={(e) => onChange("nazwa", e.target.value)}
          required
          className="w-full p-2 bg-gray-600 border border-gray-500 rounded text-white text-sm focus:ring-2 focus:ring-purple-500"
          placeholder="Nazwa materiału"
        />
      </div>
      <div>
        <label className="block text-sm text-gray-300 mb-1">Typ</label>
        <input
          type="text"
          value={data.typ || ""}
          onChange={(e) => onChange("typ", e.target.value)}
          className="w-full p-2 bg-gray-600 border border-gray-500 rounded text-white text-sm focus:ring-2 focus:ring-purple-500"
          placeholder="Typ materiału"
        />
      </div>
      <div className="col-span-2">
        <label className="block text-sm text-gray-300 mb-1">Info</label>
        <textarea
          value={data.info || ""}
          onChange={(e) => onChange("info", e.target.value)}
          rows={3}
          className="w-full p-2 bg-gray-600 border border-gray-500 rounded text-white text-sm focus:ring-2 focus:ring-purple-500 resize-none"
          placeholder="Dodatkowe informacje"
        />
      </div>
    </div>
  );
}

// Table Components
interface TableProps<T> {
  data: T[];
  editingId: number | null;
  onEdit: (id: number | null) => void;
  onSave: (id: number, data: any) => void;
  onDelete: (id: number) => void;
  saving: boolean;
}

export function ProduktyTable({
  data,
  editingId,
  onEdit,
  onSave,
  onDelete,
  saving,
}: TableProps<any>) {
  const [editData, setEditData] = useState<any>({});

  const handleEdit = (item: any) => {
    setEditData(item);
    onEdit(item.id);
  };

  const handleSave = () => {
    onSave(editingId!, editData);
  };

  const handleCancel = () => {
    setEditData({});
    onEdit(null);
  };

  return (
    <table className="w-full text-sm text-gray-300">
      <thead>
        <tr className="border-b border-gray-600">
          <th className="text-left p-2">ID</th>
          <th className="text-left p-2">Kod handlowy</th>
          <th className="text-left p-2">Nazwa</th>
          <th className="text-left p-2">Metraż</th>
          <th className="text-left p-2">Wydajność</th>
          <th className="text-left p-2">Surowiec</th>
          <th className="text-left p-2">Materiał</th>
          <th className="text-left p-2">Mat. %</th>
          <th className="text-left p-2">Akcje</th>
        </tr>
      </thead>
      <tbody>
        {data.map((item) => (
          <tr
            key={item.id}
            className="border-b border-gray-700 hover:bg-gray-700"
          >
            <td className="p-2">{item.id}</td>
            <td className="p-2">
              {editingId === item.id ? (
                <input
                  type="text"
                  value={editData.kod_handlowy || ""}
                  onChange={(e) =>
                    setEditData({ ...editData, kod_handlowy: e.target.value })
                  }
                  className="w-full p-1 bg-gray-600 border border-gray-500 rounded text-white text-xs"
                />
              ) : (
                item.kod_handlowy || "-"
              )}
            </td>
            <td className="p-2">
              {editingId === item.id ? (
                <input
                  type="text"
                  value={editData.nazwa || ""}
                  onChange={(e) =>
                    setEditData({ ...editData, nazwa: e.target.value })
                  }
                  className="w-full p-1 bg-gray-600 border border-gray-500 rounded text-white text-xs"
                />
              ) : (
                item.nazwa || "-"
              )}
            </td>
            <td className="p-2">
              {editingId === item.id ? (
                <input
                  type="number"
                  step="0.01"
                  value={editData.metraz || ""}
                  onChange={(e) =>
                    setEditData({
                      ...editData,
                      metraz: parseFloat(e.target.value) || 0,
                    })
                  }
                  className="w-full p-1 bg-gray-600 border border-gray-500 rounded text-white text-xs"
                />
              ) : (
                item.metraz
              )}
            </td>
            <td className="p-2">
              {editingId === item.id ? (
                <input
                  type="number"
                  step="0.01"
                  value={editData.wydajnosc || ""}
                  onChange={(e) =>
                    setEditData({
                      ...editData,
                      wydajnosc: parseFloat(e.target.value) || 0,
                    })
                  }
                  className="w-full p-1 bg-gray-600 border border-gray-500 rounded text-white text-xs"
                />
              ) : (
                item.wydajnosc || "-"
              )}
            </td>
            <td className="p-2">
              {editingId === item.id ? (
                <input
                  type="text"
                  value={editData.surowiec || ""}
                  onChange={(e) =>
                    setEditData({ ...editData, surowiec: e.target.value })
                  }
                  className="w-full p-1 bg-gray-600 border border-gray-500 rounded text-white text-xs"
                />
              ) : (
                item.surowiec || "-"
              )}
            </td>
            <td className="p-2">
              {editingId === item.id ? (
                <input
                  type="text"
                  value={editData.material || ""}
                  onChange={(e) =>
                    setEditData({ ...editData, material: e.target.value })
                  }
                  className="w-full p-1 bg-gray-600 border border-gray-500 rounded text-white text-xs"
                />
              ) : (
                item.material || "-"
              )}
            </td>
            <td className="p-2">
              {editingId === item.id ? (
                <input
                  type="number"
                  step="0.1"
                  value={editData.material_percent || ""}
                  onChange={(e) =>
                    setEditData({
                      ...editData,
                      material_percent: parseFloat(e.target.value) || 0,
                    })
                  }
                  className="w-full p-1 bg-gray-600 border border-gray-500 rounded text-white text-xs"
                />
              ) : (
                item.material_percent || "-"
              )}
            </td>
            <td className="p-2">
              {editingId === item.id ? (
                <div className="flex space-x-1">
                  <button
                    onClick={handleSave}
                    disabled={saving}
                    className="bg-green-600 hover:bg-green-700 disabled:bg-gray-600 text-white px-2 py-1 rounded text-xs"
                  >
                    💾
                  </button>
                  <button
                    onClick={handleCancel}
                    className="bg-gray-600 hover:bg-gray-700 text-white px-2 py-1 rounded text-xs"
                  >
                    ❌
                  </button>
                </div>
              ) : (
                <div className="flex space-x-1">
                  <button
                    onClick={() => handleEdit(item)}
                    className="bg-blue-600 hover:bg-blue-700 text-white px-2 py-1 rounded text-xs"
                  >
                    ✏️
                  </button>
                  <button
                    onClick={() => onDelete(item.id)}
                    className="bg-red-600 hover:bg-red-700 text-white px-2 py-1 rounded text-xs"
                  >
                    🗑️
                  </button>
                </div>
              )}
            </td>
          </tr>
        ))}
      </tbody>
    </table>
  );
}
