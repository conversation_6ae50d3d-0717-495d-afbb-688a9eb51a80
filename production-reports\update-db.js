const Database = require("better-sqlite3");
const path = require("path");

try {
  const dbPath = path.join(__dirname, "baza_danych.db");
  const db = new Database(dbPath);

  console.log("Adding missing columns to raporty_produkcyjne table...");

  // Check if columns already exist
  const tableInfo = db.prepare("PRAGMA table_info(raporty_produkcyjne)").all();
  const existingColumns = tableInfo.map(col => col.name);

  console.log("Existing columns:", existingColumns);

  // Add nr_zamowienia column if it doesn't exist
  if (!existingColumns.includes('nr_zamowienia')) {
    db.exec("ALTER TABLE raporty_produkcyjne ADD COLUMN nr_zamowienia TEXT;");
    console.log("Added nr_zamowienia column");
  } else {
    console.log("nr_zamowienia column already exists");
  }

  // Add waga column if it doesn't exist
  if (!existingColumns.includes('waga')) {
    db.exec("ALTER TABLE raporty_produkcyjne ADD COLUMN waga REAL;");
    console.log("Added waga column");
  } else {
    console.log("waga column already exists");
  }

  // Add szarza_surowca column if it doesn't exist
  if (!existingColumns.includes('szarza_surowca')) {
    db.exec("ALTER TABLE raporty_produkcyjne ADD COLUMN szarza_surowca TEXT;");
    console.log("Added szarza_surowca column");
  } else {
    console.log("szarza_surowca column already exists");
  }

  // Verify the changes
  const updatedTableInfo = db.prepare("PRAGMA table_info(raporty_produkcyjne)").all();
  console.log("Updated columns:", updatedTableInfo.map(col => col.name));

  db.close();
  console.log("Database update completed successfully!");

} catch (error) {
  console.error("Error updating database:", error);
}
