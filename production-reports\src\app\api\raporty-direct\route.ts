import { NextRequest, NextResponse } from "next/server";
const Database = require("better-sqlite3");
import path from "path";

export async function GET() {
  let db: any = null;

  try {
    const dbPath = path.join(process.cwd(), "prisma", "baza_danych.db");
    db = new Database(dbPath);

    // Check and add missing columns if they don't exist
    try {
      const tableInfo = db
        .prepare("PRAGMA table_info(raporty_produkcyjne)")
        .all();
      const existingColumns = tableInfo.map((col: any) => col.name);

      if (!existingColumns.includes("nr_zamowienia")) {
        db.exec(
          "ALTER TABLE raporty_produkcyjne ADD COLUMN nr_zamowienia TEXT;"
        );
        console.log("Added nr_zamowienia column");
      }

      if (!existingColumns.includes("waga")) {
        db.exec("ALTER TABLE raporty_produkcyjne ADD COLUMN waga REAL;");
        console.log("Added waga column");
      }

      if (!existingColumns.includes("szarza_surowca")) {
        db.exec(
          "ALTER TABLE raporty_produkcyjne ADD COLUMN szarza_surowca TEXT;"
        );
        console.log("Added szarza_surowca column");
      }
    } catch (alterError: any) {
      console.log(
        "Note: Could not add columns (they may already exist):",
        alterError.message
      );
    }

    const raporty = db
      .prepare(
        `
      SELECT
        r.*,
        p.kod_handlowy as produkt_kod,
        p.nazwa as produkt_nazwa,
        m.nazwa as maszyna_nazwa,
        pr.imie as pracownik_imie,
        pr.nazwisko as pracownik_nazwisko,
        s.nazwa as surowiec_nazwa,
        mat.nazwa as material_nazwa
      FROM raporty_produkcyjne r
      LEFT JOIN produkty p ON r.produkt_id = p.id
      LEFT JOIN maszyny m ON r.maszyna_id = m.id
      LEFT JOIN pracownicy pr ON r.pracownik_id = pr.id
      LEFT JOIN surowce s ON r.surowiec_id = s.id
      LEFT JOIN materialy mat ON r.material_id = mat.id
      ORDER BY r.data_utworzenia DESC
    `
      )
      .all();

    return NextResponse.json(raporty);
  } catch (error) {
    console.error("Błąd podczas pobierania raportów:", error);
    return NextResponse.json(
      { error: "Błąd podczas pobierania raportów" },
      { status: 500 }
    );
  } finally {
    if (db) {
      db.close();
    }
  }
}

export async function POST(request: NextRequest) {
  let db: any = null;

  try {
    const data = await request.json();
    console.log("Received data for report creation:", data);

    const dbPath = path.join(process.cwd(), "prisma", "baza_danych.db");
    db = new Database(dbPath);

    // Check and add missing columns if they don't exist
    try {
      const tableInfo = db
        .prepare("PRAGMA table_info(raporty_produkcyjne)")
        .all();
      const existingColumns = tableInfo.map((col: any) => col.name);

      if (!existingColumns.includes("nr_zamowienia")) {
        db.exec(
          "ALTER TABLE raporty_produkcyjne ADD COLUMN nr_zamowienia TEXT;"
        );
        console.log("Added nr_zamowienia column");
      }

      if (!existingColumns.includes("waga")) {
        db.exec("ALTER TABLE raporty_produkcyjne ADD COLUMN waga REAL;");
        console.log("Added waga column");
      }

      if (!existingColumns.includes("szarza_surowca")) {
        db.exec(
          "ALTER TABLE raporty_produkcyjne ADD COLUMN szarza_surowca TEXT;"
        );
        console.log("Added szarza_surowca column");
      }
    } catch (alterError: any) {
      console.log(
        "Note: Could not add columns (they may already exist):",
        alterError.message
      );
    }

    // The frontend sends arrays for surowce and materialy.
    // The database expects a single entry. We'll take the first one from each array.
    const surowiec =
      data.surowce && data.surowce.length > 0 ? data.surowce[0] : {};
    const material =
      data.materialy && data.materialy.length > 0 ? data.materialy[0] : {};

    // Insert the report
    const insertReport = db.prepare(`
      INSERT INTO raporty_produkcyjne (
        data_utworzenia,
        produkt_id,
        metraz_rolek,
        ilosc_rolek,
        nr_zamowienia,
        waga,
        surowiec_id,
        zuzyty_surowiec,
        odpad_surowiec,
        szarza_surowca,
        material_id,
        zuzyty_material,
        odpad_material,
        maszyna_id,
        pracownik_id,
        czas_pracy_maszyny,
        czas_pracy_pracownika,
        uwagi
      ) VALUES (
        datetime('now'),
        ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?
      )
    `);

    // Validate required fields
    if (!surowiec.surowiec_id || !material.material_id) {
      throw new Error("Missing surowiec_id or material_id");
    }

    const result = insertReport.run(
      data.produkt_id,
      data.metraz_rolek,
      data.ilosc_rolek,
      data.nr_zamowienia || null,
      data.waga || null,
      surowiec.surowiec_id,
      surowiec.zuzyty_surowiec || 0,
      surowiec.odpad_surowiec || 0,
      surowiec.szarza || null,
      material.material_id,
      material.zuzyty_material || 0,
      material.odpad_material || 0,
      data.maszyna_id,
      data.pracownik_id,
      data.czas_pracy_maszyny,
      data.czas_pracy_pracownika,
      data.uwagi || null
    );

    console.log("Report inserted with ID:", result.lastInsertRowid);

    // Fetch the created report with related data
    const createdReport = db
      .prepare(
        `
      SELECT
        r.*,
        p.kod_handlowy as produkt_kod,
        p.nazwa as produkt_nazwa,
        m.nazwa as maszyna_nazwa,
        pr.imie as pracownik_imie,
        pr.nazwisko as pracownik_nazwisko,
        s.nazwa as surowiec_nazwa,
        mat.nazwa as material_nazwa
      FROM raporty_produkcyjne r
      LEFT JOIN produkty p ON r.produkt_id = p.id
      LEFT JOIN maszyny m ON r.maszyna_id = m.id
      LEFT JOIN pracownicy pr ON r.pracownik_id = pr.id
      LEFT JOIN surowce s ON r.surowiec_id = s.id
      LEFT JOIN materialy mat ON r.material_id = mat.id
      WHERE r.id = ?
    `
      )
      .get(result.lastInsertRowid);

    return NextResponse.json(createdReport, { status: 201 });
  } catch (error) {
    console.error("Błąd podczas tworzenia raportu:", error);
    return NextResponse.json(
      {
        error: "Błąd podczas tworzenia raportu",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  } finally {
    if (db) {
      db.close();
    }
  }
}
