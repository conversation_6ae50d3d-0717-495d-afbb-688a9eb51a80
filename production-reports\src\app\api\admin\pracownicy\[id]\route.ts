import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id);
    const data = await request.json();

    const pracownik = await prisma.pracownik.update({
      where: { id },
      data: {
        numer: data.numer,
        imie: data.imie || null,
        nazwisko: data.nazwisko || null,
        stanowisko: data.stanowisko || null,
      },
    });

    return NextResponse.json(pracownik);
  } catch (error) {
    console.error("Błąd podczas aktualizacji pracownika:", error);
    return NextResponse.json(
      { error: "Błąd podczas aktualizacji pracownika" },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id);

    await prisma.pracownik.delete({
      where: { id },
    });

    return NextResponse.json({ message: "Pracownik został usunięty" });
  } catch (error) {
    console.error("Błąd podczas usuwania pracownika:", error);
    return NextResponse.json(
      { error: "Błąd podczas usuwania pracownika" },
      { status: 500 }
    );
  }
}
