-- CreateTable
CREATE TABLE "produkty" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "kod_handlowy" TEXT,
    "nazwa" TEXT,
    "metraz" REAL NOT NULL,
    "surowiec" TEXT,
    "material" TEXT,
    "info" TEXT
);

-- CreateTable
CREATE TABLE "maszyny" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "nazwa" TEXT NOT NULL,
    "typ" TEXT,
    "info" TEXT
);

-- CreateTable
CREATE TABLE "pracownicy" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "numer" TEXT NOT NULL,
    "imie" TEXT,
    "nazwisko" TEXT,
    "stanowisko" TEXT
);

-- CreateTable
CREATE TABLE "surowce" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "nazwa" TEXT NOT NULL,
    "typ" TEXT,
    "info" TEXT
);

-- CreateTable
CREATE TABLE "materialy" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "nazwa" TEXT NOT NULL,
    "typ" TEXT,
    "info" TEXT
);

-- CreateTable
CREATE TABLE "raporty_produkcyjne" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "data_utworzenia" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "produkt_id" INTEGER NOT NULL,
    "metraz_rolek" REAL NOT NULL,
    "ilosc_rolek" INTEGER NOT NULL,
    "surowiec_id" INTEGER NOT NULL,
    "zuzyty_surowiec" REAL NOT NULL,
    "odpad_surowiec" REAL NOT NULL,
    "material_id" INTEGER NOT NULL,
    "zuzyty_material" REAL NOT NULL,
    "odpad_material" REAL NOT NULL,
    "maszyna_id" INTEGER NOT NULL,
    "pracownik_id" INTEGER NOT NULL,
    "czas_pracy_maszyny" REAL NOT NULL,
    "czas_pracy_pracownika" REAL NOT NULL,
    "uwagi" TEXT,
    CONSTRAINT "raporty_produkcyjne_produkt_id_fkey" FOREIGN KEY ("produkt_id") REFERENCES "produkty" ("id") ON DELETE RESTRICT ON UPDATE CASCADE,
    CONSTRAINT "raporty_produkcyjne_surowiec_id_fkey" FOREIGN KEY ("surowiec_id") REFERENCES "surowce" ("id") ON DELETE RESTRICT ON UPDATE CASCADE,
    CONSTRAINT "raporty_produkcyjne_material_id_fkey" FOREIGN KEY ("material_id") REFERENCES "materialy" ("id") ON DELETE RESTRICT ON UPDATE CASCADE,
    CONSTRAINT "raporty_produkcyjne_maszyna_id_fkey" FOREIGN KEY ("maszyna_id") REFERENCES "maszyny" ("id") ON DELETE RESTRICT ON UPDATE CASCADE,
    CONSTRAINT "raporty_produkcyjne_pracownik_id_fkey" FOREIGN KEY ("pracownik_id") REFERENCES "pracownicy" ("id") ON DELETE RESTRICT ON UPDATE CASCADE
);

-- CreateIndex
CREATE UNIQUE INDEX "pracownicy_numer_key" ON "pracownicy"("numer");
