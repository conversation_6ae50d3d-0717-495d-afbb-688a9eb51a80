import { NextRequest, NextResponse } from "next/server";

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  let db;

  try {
    const Database = require("better-sqlite3");
    const path = require("path");

    const dbPath = path.join(process.cwd(), "prisma", "baza_danych.db");
    db = new Database(dbPath);

    const id = parseInt(params.id);

    // Fetch the report with related data
    const report = db
      .prepare(
        `
      SELECT
        r.*,
        p.kod_handlowy as produkt_kod,
        p.nazwa as produkt_nazwa,
        m.nazwa as maszyna_nazwa,
        pr.imie as pracownik_imie,
        pr.nazwisko as pracownik_nazwisko,
        s.nazwa as surowiec_nazwa,
        mat.nazwa as material_nazwa
      FROM raporty_produkcyjne r
      LEFT JOIN produkty p ON r.produkt_id = p.id
      LEFT JOIN maszyny m ON r.maszyna_id = m.id
      LEFT JOIN pracownicy pr ON r.pracownik_id = pr.id
      LEFT JOIN surowce s ON r.surowiec_id = s.id
      LEFT JOIN materialy mat ON r.material_id = mat.id
      WHERE r.id = ?
    `
      )
      .get(id);

    if (!report) {
      return NextResponse.json(
        { error: "Raport nie został znaleziony" },
        { status: 404 }
      );
    }

    return NextResponse.json(report);
  } catch (error) {
    console.error("Błąd podczas pobierania raportu:", error);
    return NextResponse.json(
      { error: "Błąd podczas pobierania raportu" },
      { status: 500 }
    );
  } finally {
    if (db) {
      db.close();
    }
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  let db;

  try {
    const Database = require("better-sqlite3");
    const path = require("path");

    const dbPath = path.join(process.cwd(), "prisma", "baza_danych.db");
    db = new Database(dbPath);

    // Check and add missing columns if they don't exist
    try {
      const tableInfo = db
        .prepare("PRAGMA table_info(raporty_produkcyjne)")
        .all();
      const existingColumns = tableInfo.map((col: any) => col.name);

      if (!existingColumns.includes("nr_zamowienia")) {
        db.exec(
          "ALTER TABLE raporty_produkcyjne ADD COLUMN nr_zamowienia TEXT;"
        );
        console.log("Added nr_zamowienia column");
      }

      if (!existingColumns.includes("waga")) {
        db.exec("ALTER TABLE raporty_produkcyjne ADD COLUMN waga REAL;");
        console.log("Added waga column");
      }

      if (!existingColumns.includes("szarza_surowca")) {
        db.exec(
          "ALTER TABLE raporty_produkcyjne ADD COLUMN szarza_surowca TEXT;"
        );
        console.log("Added szarza_surowca column");
      }
    } catch (alterError: any) {
      console.log(
        "Note: Could not add columns (they may already exist):",
        alterError.message
      );
    }

    const id = parseInt(params.id);
    const data = await request.json();

    // Update the report
    const updateReport = db.prepare(`
      UPDATE raporty_produkcyjne SET
        produkt_id = ?,
        metraz_rolek = ?,
        ilosc_rolek = ?,
        nr_zamowienia = ?,
        waga = ?,
        surowiec_id = ?,
        zuzyty_surowiec = ?,
        odpad_surowiec = ?,
        szarza_surowca = ?,
        material_id = ?,
        zuzyty_material = ?,
        odpad_material = ?,
        maszyna_id = ?,
        pracownik_id = ?,
        czas_pracy_maszyny = ?,
        czas_pracy_pracownika = ?,
        uwagi = ?
      WHERE id = ?
    `);

    const result = updateReport.run(
      data.produkt_id,
      data.metraz_rolek,
      data.ilosc_rolek,
      data.nr_zamowienia || null,
      data.waga || null,
      data.surowiec_id,
      data.zuzyty_surowiec,
      data.odpad_surowiec,
      data.szarza_surowca || null,
      data.material_id,
      data.zuzyty_material,
      data.odpad_material,
      data.maszyna_id,
      data.pracownik_id,
      data.czas_pracy_maszyny,
      data.czas_pracy_pracownika,
      data.uwagi || null,
      id
    );

    if (result.changes === 0) {
      return NextResponse.json(
        { error: "Raport nie został znaleziony" },
        { status: 404 }
      );
    }

    // Fetch the updated report with related data
    const updatedReport = db
      .prepare(
        `
      SELECT
        r.*,
        p.kod_handlowy as produkt_kod,
        p.nazwa as produkt_nazwa,
        m.nazwa as maszyna_nazwa,
        pr.imie as pracownik_imie,
        pr.nazwisko as pracownik_nazwisko,
        s.nazwa as surowiec_nazwa,
        mat.nazwa as material_nazwa
      FROM raporty_produkcyjne r
      LEFT JOIN produkty p ON r.produkt_id = p.id
      LEFT JOIN maszyny m ON r.maszyna_id = m.id
      LEFT JOIN pracownicy pr ON r.pracownik_id = pr.id
      LEFT JOIN surowce s ON r.surowiec_id = s.id
      LEFT JOIN materialy mat ON r.material_id = mat.id
      WHERE r.id = ?
    `
      )
      .get(id);

    return NextResponse.json(updatedReport);
  } catch (error) {
    console.error("Błąd podczas aktualizacji raportu:", error);
    return NextResponse.json(
      { error: "Błąd podczas aktualizacji raportu" },
      { status: 500 }
    );
  } finally {
    if (db) {
      db.close();
    }
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  let db;

  try {
    const Database = require("better-sqlite3");
    const path = require("path");

    const dbPath = path.join(process.cwd(), "prisma", "baza_danych.db");
    db = new Database(dbPath);

    // Check and add missing columns if they don't exist (for consistency)
    try {
      const tableInfo = db
        .prepare("PRAGMA table_info(raporty_produkcyjne)")
        .all();
      const existingColumns = tableInfo.map((col: any) => col.name);

      if (!existingColumns.includes("nr_zamowienia")) {
        db.exec(
          "ALTER TABLE raporty_produkcyjne ADD COLUMN nr_zamowienia TEXT;"
        );
        console.log("Added nr_zamowienia column");
      }

      if (!existingColumns.includes("waga")) {
        db.exec("ALTER TABLE raporty_produkcyjne ADD COLUMN waga REAL;");
        console.log("Added waga column");
      }

      if (!existingColumns.includes("szarza_surowca")) {
        db.exec(
          "ALTER TABLE raporty_produkcyjne ADD COLUMN szarza_surowca TEXT;"
        );
        console.log("Added szarza_surowca column");
      }
    } catch (alterError: any) {
      console.log(
        "Note: Could not add columns (they may already exist):",
        alterError.message
      );
    }

    const id = parseInt(params.id);

    // Delete the report
    const deleteReport = db.prepare(`
      DELETE FROM raporty_produkcyjne WHERE id = ?
    `);

    const result = deleteReport.run(id);

    if (result.changes === 0) {
      return NextResponse.json(
        { error: "Raport nie został znaleziony" },
        { status: 404 }
      );
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Błąd podczas usuwania raportu:", error);
    return NextResponse.json(
      { error: "Błąd podczas usuwania raportu" },
      { status: 500 }
    );
  } finally {
    if (db) {
      db.close();
    }
  }
}
