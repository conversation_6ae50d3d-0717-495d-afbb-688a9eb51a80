import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id);
    const data = await request.json();

    const surowiec = await prisma.surowiec.update({
      where: { id },
      data: {
        nazwa: data.nazwa,
        typ: data.typ || null,
        info: data.info || null,
      },
    });

    return NextResponse.json(surowiec);
  } catch (error) {
    console.error("Błąd podczas aktualizacji surowca:", error);
    return NextResponse.json(
      { error: "Błąd podczas aktualizacji surowca" },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id);

    await prisma.surowiec.delete({
      where: { id },
    });

    return NextResponse.json({ message: "Surowiec został usunięty" });
  } catch (error) {
    console.error("Błąd podczas usuwania surowca:", error);
    return NextResponse.json(
      { error: "Błąd podczas usuwania surowca" },
      { status: 500 }
    );
  }
}
