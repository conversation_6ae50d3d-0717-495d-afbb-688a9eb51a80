import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";

export async function GET() {
  try {
    const produkty = await prisma.produkty.findMany({
      orderBy: {
        kod_handlowy: "asc",
      },
    });

    return NextResponse.json(produkty);
  } catch (error) {
    console.error("Błąd podczas pobierania produktów:", error);
    return NextResponse.json(
      { error: "Błąd podczas pobierania produktów" },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const data = await request.json();

    // Use direct database access to handle new material fields
    const Database = require("better-sqlite3");
    const path = require("path");

    const dbPath = path.join(process.cwd(), "prisma", "baza_danych.db");
    const db = new Database(dbPath);

    // Insert the product with all fields
    const insertStmt = db.prepare(`
      INSERT INTO produkty (
        kod_handlowy, nazwa, metraz, surowiec, material, material_percent,
        material1, material1_percent, material2, material2_percent,
        material3, material3_percent, material4, material4_percent,
        material5, material5_percent, info, wydajnosc
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);

    const result = insertStmt.run(
      data.kod_handlowy || null,
      data.nazwa || null,
      data.metraz || 0,
      data.surowiec || null,
      data.material || null,
      data.material_percent || null,
      data.material1 || null,
      data.material1_percent || null,
      data.material2 || null,
      data.material2_percent || null,
      data.material3 || null,
      data.material3_percent || null,
      data.material4 || null,
      data.material4_percent || null,
      data.material5 || null,
      data.material5_percent || null,
      data.info || null,
      data.wydajnosc || null
    );

    // Get the created product
    const produkt = db
      .prepare("SELECT * FROM produkty WHERE id = ?")
      .get(result.lastInsertRowid);

    db.close();

    return NextResponse.json(produkt);
  } catch (error) {
    console.error("Błąd podczas dodawania produktu:", error);
    return NextResponse.json(
      { error: "Błąd podczas dodawania produktu: " + error.message },
      { status: 500 }
    );
  }
}
